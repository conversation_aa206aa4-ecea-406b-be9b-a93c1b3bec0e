import { expect } from '@playwright/test';
import { test } from '../../../fixtures/default-fixture';
import { CourseVersionStatus } from '../../../../shared/repositories/lms/constants/enums/course-version.enum';
import { ObjectiveCourse } from '../../../elements/pages/backoffice/create-course-elements';

test.beforeEach(async ({ courseVersionsRepo, enrollmentsRepo, instructorRepo, configuration, coursesRepo }) => {
  const regularCourseVersions = configuration.shareCourses.regularCourseVersion.courseVersions[1];
  const impactCourseVersion = configuration.shareCourses.regularCourseImpactVersion.courseVersions[2];
  const tsiCourseName = '[Automate][TEMP] Admin create TSI course';
  const tsiCourseCode = 'AUTOMATE_TSI_TEMP';

  const courseVersions = [
    configuration.shareCourses.regularCourseVersion.courseVersions[2],
    configuration.shareCourses.regularCourseVersion.courseVersions[3],
    { name: tsiCourseName },
  ];

  const usersEnrollCourseVersion = [configuration.shareUsers.userMember1TLI, configuration.shareUsers.userMember2TLI];

  // Clean up test instructor data
  await instructorRepo.deleteByEmail('<EMAIL>');

  for (const versions of courseVersions) {
    await courseVersionsRepo.deleteByCourseName(versions.name);
  }

  await coursesRepo.deleteCourseByCode(tsiCourseCode);

  await courseVersionsRepo.updateByCourseVersion(regularCourseVersions.id, CourseVersionStatus.published);

  await courseVersionsRepo.deleteByCourseStatus(impactCourseVersion.courseId, CourseVersionStatus.draft);

  for (const users of usersEnrollCourseVersion) {
    await enrollmentsRepo.deleteAllEnrollmentsForUser(users.guid);
  }
});

test.afterEach(async ({ courseVersionsRepo, enrollmentsRepo, instructorRepo, configuration, coursesRepo }) => {
  const regularCourseVersions = configuration.shareCourses.regularCourseVersion.courseVersions[1];
  const impactCourseVersion = configuration.shareCourses.regularCourseImpactVersion.courseVersions[2];
  const tsiCourseName = '[Automate][TEMP] Admin create TSI course';
  const tsiCourseCode = 'AUTOMATE_TSI_TEMP';

  const regularCourseNewVersion = [
    configuration.shareCourses.regularCourseVersion.courseVersions[2],
    configuration.shareCourses.regularCourseVersion.courseVersions[3],
    { name: tsiCourseName },
  ];

  const usersEnrollCourseVersion = [configuration.shareUsers.userMember1TLI, configuration.shareUsers.userMember2TLI];

  // Clean up test instructor data
  await instructorRepo.deleteByEmail('<EMAIL>');

  for (const versions of regularCourseNewVersion) {
    await courseVersionsRepo.deleteByCourseName(versions.name);
  }

  await coursesRepo.deleteCourseByCode(tsiCourseCode);

  await courseVersionsRepo.updateByCourseVersion(regularCourseVersions.id, CourseVersionStatus.published);

  await courseVersionsRepo.deleteByCourseStatus(impactCourseVersion.courseId, CourseVersionStatus.draft);

  for (const users of usersEnrollCourseVersion) {
    await enrollmentsRepo.deleteAllEnrollmentsForUser(users.guid);
  }
});

test.describe('Admin - Course Management (CPD)', () => {
  test('@SKL-T20189 Admin เข้าถึงหน้าหลักสูตรทั้งหมด, แสดงหน้าตารางรายการหลักสูตรทั้งหมด', async ({
    configuration,
    manageCourseListPage,
    adminDashboardPage,
    homePage,
    loginCpdPage,
    loginSSOPage,
  }) => {
    const adminCPD = configuration.shareUsers.userAdminCreateUserCPD;

    // Admin login
    await loginCpdPage.accessLoginSSO();
    await loginSSOPage.inputUserName(adminCPD.citizenId);
    await loginSSOPage.inputPassword(adminCPD.password);
    await loginSSOPage.submit();
    await expect(homePage.userProfileLocator).toBeVisible();

    await adminDashboardPage.accessCoursesMenu();

    await expect(manageCourseListPage.courseRowsLocator.first()).toBeVisible();
    await expect(manageCourseListPage.searchInputLocator).toBeVisible();
    await expect(manageCourseListPage.searchSubmitBtnLocator).toBeVisible();
    await expect(manageCourseListPage.createCourseButtonLocator).toBeVisible();
  });

  test('@SKL-T20192 Admin สร้างหลักสูตรการอบรมวิชาชีพการลงทุน (TSI) และกรอกข้อมูลถูกต้อง, สร้างหลักสูตรสำเร็จ', async ({
    configuration,
    manageCourseListPage,
    organizationAdminDashboardPage,
    loginCpdPage,
    loginSSOPage,
  }) => {
    const adminCPD = configuration.shareUsers.userAdminCreateUserCPD;
    const courseName = '[Automate][TEMP] Admin create TSI course';
    const courseCode = 'AUTOMATE_TSI_TEMP';

    // Admin login
    await loginCpdPage.accessLoginSSO();
    await loginSSOPage.inputUserName(adminCPD.citizenId);
    await loginSSOPage.inputPassword(adminCPD.password);
    await loginSSOPage.submit();

    await organizationAdminDashboardPage.accessManageCourseList();
    await expect(manageCourseListPage.courseRowsLocator.first()).toBeVisible();
    await expect(manageCourseListPage.searchInputLocator).toBeVisible();
    await expect(manageCourseListPage.searchSubmitBtnLocator).toBeVisible();
    await expect(manageCourseListPage.createCourseButtonLocator).toBeVisible();
    await manageCourseListPage.createCourse();
    await manageCourseListPage.createCourseForm.selectObjectiveCourse(ObjectiveCourse.TSI);
    await manageCourseListPage.createCourseForm.fillCourseCode(courseCode);
    await manageCourseListPage.createCourseForm.fillCourseName(courseName);
    await manageCourseListPage.createCourseForm.submitCreateCourseButton();
    await expect(manageCourseListPage.toastMsg.validateMsgLocator).toContainText('สร้างหลักสูตรสำเร็จ');
  });

  test('@SKL-T20201 Admin แก้ไขข้อมูลคุณสมบัติหลักสูตรของหลักสูตรการอบรมวิชาชีพการลงทุน (TSI) กรณีกรอกไม่ข้อมูลถูกต้อง, แสดง error และไม่สามารถบันทึกได้', async ({
    configuration,
    manageCourseListPage,
    organizationAdminDashboardPage,
    manageCourseDetailPage,
    loginCpdPage,
    loginSSOPage,
  }) => {
    const adminCPD = configuration.shareUsers.userAdminCreateUserCPD;
    const courseName = '[Automate][TEMP] Admin create TSI course';
    const courseCode = 'AUTOMATE_TSI_TEMP';

    // Admin login
    await loginCpdPage.accessLoginSSO();
    await loginSSOPage.inputUserName(adminCPD.citizenId);
    await loginSSOPage.inputPassword(adminCPD.password);
    await loginSSOPage.submit();

    await organizationAdminDashboardPage.accessManageCourseList();

    // Create TSI course first
    await expect(manageCourseListPage.courseRowsLocator.first()).toBeVisible();
    await expect(manageCourseListPage.searchInputLocator).toBeVisible();
    await expect(manageCourseListPage.searchSubmitBtnLocator).toBeVisible();
    await expect(manageCourseListPage.createCourseButtonLocator).toBeVisible();
    await manageCourseListPage.createCourse();
    await manageCourseListPage.createCourseForm.selectObjectiveCourse(ObjectiveCourse.TSI);
    await manageCourseListPage.createCourseForm.fillCourseCode(courseCode);
    await manageCourseListPage.createCourseForm.fillCourseName(courseName);
    await manageCourseListPage.createCourseForm.submitCreateCourseButton();
    await expect(manageCourseListPage.toastMsg.validateMsgLocator).toContainText('สร้างหลักสูตรสำเร็จ');

    // Access edit course properties
    await manageCourseDetailPage.editCoursePropertiesButtonLocator.click();

    // Try to save without filling required fields
    await manageCourseDetailPage.saveButtonLocator.click();

    // Validate error messages when empty (TSI only has 2 fields)
    await expect(manageCourseDetailPage.trainingCenterErrorLocator).toContainText('กรุณาเลือกศูนย์อบรม');
    await expect(manageCourseDetailPage.applicantTypeErrorLocator).toContainText('กรุณาเลือกประเภทผู้สมัคร');

    // @SKL-T20199 Admin แก้ไขข้อมูลคุณสมบัติหลักสูตรของหลักสูตรการอบรมวิชาชีพการลงทุน (TSI) กรณีกรอกข้อมูลถูกต้อง, สามารถแก้ไขได้และบันทึกสำเร็จ
  });

  test('', async ({
    configuration,
    manageCourseListPage,
    organizationAdminDashboardPage,
    manageCourseDetailPage,
    loginCpdPage,
    loginSSOPage,
  }) => {
    const adminCPD = configuration.shareUsers.userAdminCreateUserCPD;
    const courseName = '[Automate][TEMP] Admin create TSI course';
    const courseCode = 'AUTOMATE_TSI_TEMP';

    // Admin login
    await loginCpdPage.accessLoginSSO();
    await loginSSOPage.inputUserName(adminCPD.citizenId);
    await loginSSOPage.inputPassword(adminCPD.password);
    await loginSSOPage.submit();

    await organizationAdminDashboardPage.accessManageCourseList();

    // Create TSI course first
    await expect(manageCourseListPage.courseRowsLocator.first()).toBeVisible();
    await expect(manageCourseListPage.searchInputLocator).toBeVisible();
    await expect(manageCourseListPage.searchSubmitBtnLocator).toBeVisible();
    await expect(manageCourseListPage.createCourseButtonLocator).toBeVisible();
    await manageCourseListPage.createCourse();
    await manageCourseListPage.createCourseForm.selectObjectiveCourse(ObjectiveCourse.TSI);
    await manageCourseListPage.createCourseForm.fillCourseCode(courseCode);
    await manageCourseListPage.createCourseForm.fillCourseName(courseName);
    await manageCourseListPage.createCourseForm.submitCreateCourseButton();
    await expect(manageCourseListPage.toastMsg.validateMsgLocator).toContainText('สร้างหลักสูตรสำเร็จ');

    // Access edit course properties
    await manageCourseDetailPage.editCoursePropertiesButtonLocator.click();

  
  });
});
