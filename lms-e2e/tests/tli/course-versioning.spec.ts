import { expect } from '@playwright/test';
import { test } from '../../fixtures/default-fixture';
import { CourseVersionStatus } from '../../../shared/repositories/lms/constants/enums/course-version.enum';

test.beforeEach(async ({ courseVersionsRepo, enrollmentsRepo, configuration }) => {
  const regularCourseVersions = configuration.shareCourses.regularCourseVersion.courseVersions[1];
  const impactCourseVersion = configuration.shareCourses.regularCourseImpactVersion.courseVersions[2];

  const regularCourseNewVersion = [
    configuration.shareCourses.regularCourseVersion.courseVersions[2],
    configuration.shareCourses.regularCourseVersion.courseVersions[3],
  ];

  const usersEnrollCourseVersion = [configuration.shareUsers.userMember1TLI, configuration.shareUsers.userMember2TLI];

  for (const versions of regularCourseNewVersion) {
    await courseVersionsRepo.deleteByCourseName(versions.name);
  }

  await courseVersionsRepo.updateByCourseVersion(regularCourseVersions.id, CourseVersionStatus.published);

  await courseVersionsRepo.deleteByCourseStatus(impactCourseVersion.courseId, CourseVersionStatus.draft);

  for (const users of usersEnrollCourseVersion) {
    await enrollmentsRepo.deleteAllEnrollmentsForUser(users.guid);
  }
});

test.afterEach(async ({ courseVersionsRepo, enrollmentsRepo, configuration }) => {
  const regularCourseVersions = configuration.shareCourses.regularCourseVersion.courseVersions[1];
  const impactCourseVersion = configuration.shareCourses.regularCourseImpactVersion.courseVersions[2];

  const regularCourseNewVersion = [
    configuration.shareCourses.regularCourseVersion.courseVersions[2],
    configuration.shareCourses.regularCourseVersion.courseVersions[3],
  ];

  const usersEnrollCourseVersion = [configuration.shareUsers.userMember1TLI, configuration.shareUsers.userMember2TLI];

  for (const versions of regularCourseNewVersion) {
    await courseVersionsRepo.deleteByCourseName(versions.name);
  }

  await courseVersionsRepo.updateByCourseVersion(regularCourseVersions.id, CourseVersionStatus.published);

  await courseVersionsRepo.deleteByCourseStatus(impactCourseVersion.courseId, CourseVersionStatus.draft);

  for (const users of usersEnrollCourseVersion) {
    await enrollmentsRepo.deleteAllEnrollmentsForUser(users.guid);
  }
});

test.describe('Admin - Verify course versioning', () => {
  test('@SKL-T20375 @SKL-T20376 Admin edit course name and course description verify impact course version', async ({
    configuration,
    loginPage,
    homePage,
    adminDashboardPage,
    manageCourseListPage,
    manageCourseDetailPage,
  }) => {
    const organizationAdmin = configuration.usersLocal.organizationAdminTLI1;
    const regularCourseVersion = configuration.shareCourses.regularCourseImpactVersion;

    // Admin login
    await loginPage.loginWithUsernameAndPassword(organizationAdmin.username, organizationAdmin.password);
    await expect(homePage.userProfileLocator).toBeVisible();
    await adminDashboardPage.accessCoursesMenu();
    await manageCourseListPage.searchCourseName(regularCourseVersion.courseVersions[1].name);
    await manageCourseListPage.viewCourseDetail();

    // Edit name impact course version
    await manageCourseDetailPage.accessCourseGeneralDetail(regularCourseVersion.courseVersions[1].name);
    await manageCourseDetailPage.editCourseName(regularCourseVersion.courseVersions[2].name);
    await manageCourseDetailPage.submitSaveButton();
    await manageCourseDetailPage.verifyCreateDraftSuccess();
    await manageCourseDetailPage.cancelDraftVersion();
    await manageCourseDetailPage.confirmCancelDraftVersion();

    // Edit content impact course version
    await manageCourseDetailPage.accessTabCurriculum();
    await manageCourseDetailPage.editCurriculum();
    await manageCourseDetailPage.collapsedContentInCurriculum(2);
    await manageCourseDetailPage.verifyCreateDraftSuccess();
    await manageCourseDetailPage.cancelDraftVersion();
    await manageCourseDetailPage.confirmCancelDraftVersion();
  });

  test('@SKL-T20375 @SKL-T20376 Admin add instructor and setting enroll verify impact course version', async ({
    configuration,
    loginPage,
    homePage,
    adminDashboardPage,
    manageCourseListPage,
    manageCourseDetailPage,
  }) => {
    const organizationAdmin = configuration.usersLocal.organizationAdminTLI1;
    const regularCourseVersion = configuration.shareCourses.regularCourseImpactVersion;

    // Admin login
    await loginPage.loginWithUsernameAndPassword(organizationAdmin.username, organizationAdmin.password);
    await expect(homePage.userProfileLocator).toBeVisible();
    await adminDashboardPage.accessCoursesMenu();
    await manageCourseListPage.searchCourseName(regularCourseVersion.courseVersions[1].name);
    await manageCourseListPage.viewCourseDetail();

    // Instructor impact course version
    await manageCourseDetailPage.accessMenuInstructor();
    await manageCourseDetailPage.editInstructorButtonLocator.click();
    await manageCourseDetailPage.addInstructor('nh1');
    await manageCourseDetailPage.submitSaveButton();
    await manageCourseDetailPage.verifyCreateDraftSuccess();
    await manageCourseDetailPage.cancelDraftVersion();
    await manageCourseDetailPage.confirmCancelDraftVersion();

    // Setting enrollment impact course version
    await manageCourseDetailPage.accessTabGeneralInfo();
    await manageCourseDetailPage.accessMenuEnroll();
    await manageCourseDetailPage.enabledExpiryDay();
    await manageCourseDetailPage.submitSaveButton();
    await manageCourseDetailPage.verifyCreateDraftSuccess();
    await manageCourseDetailPage.cancelDraftVersion();
    await manageCourseDetailPage.confirmCancelDraftVersion();
  });

  test('@SKL-T20375 @SKL-T20376 Admin setting certificate and edit pass criteria of quiz verify impact course version', async ({
    configuration,
    loginPage,
    homePage,
    adminDashboardPage,
    manageCourseListPage,
    manageCourseDetailPage,
  }) => {
    const organizationAdmin = configuration.usersLocal.organizationAdminTLI1;
    const regularCourseVersion = configuration.shareCourses.regularCourseImpactVersion;

    // Admin login
    await loginPage.loginWithUsernameAndPassword(organizationAdmin.username, organizationAdmin.password);
    await expect(homePage.userProfileLocator).toBeVisible();
    await adminDashboardPage.accessCoursesMenu();
    await manageCourseListPage.searchCourseName(regularCourseVersion.courseVersions[1].name);
    await manageCourseListPage.viewCourseDetail();

    // Setting certificate impact course version
    await manageCourseDetailPage.accessTabGeneralInfo();
    await manageCourseDetailPage.accessMenuCertificate();
    await manageCourseDetailPage.accessEditCertificate();
    await manageCourseDetailPage.certificateCourseDetailElementsPage.clickAddNewCertificate();
    await manageCourseDetailPage.certificateCourseDetailElementsPage.selectCertificateType(
      'อบรมวิชาชีพการประกันภัย - ขอรับ',
    );
    await manageCourseDetailPage.certificateCourseDetailElementsPage.submitSaveCertificateButton();
    await manageCourseDetailPage.verifyCreateDraftSuccess();
    await manageCourseDetailPage.cancelDraftVersion();
    await manageCourseDetailPage.confirmCancelDraftVersion();

    // Edit condition pass criteria impact course version
    await manageCourseDetailPage.accessTabGeneralInfo();
    await manageCourseDetailPage.accessMenuStudyCriteriaMenuLocator();
    await manageCourseDetailPage.accessEditCriteria();
    await manageCourseDetailPage.certificateCourseDetailElementsPage.clickEnablePassScoreCriteria();
    await manageCourseDetailPage.certificateCourseDetailElementsPage.fillPassScoreCriteria('1', 1);
    await manageCourseDetailPage.submitSaveButton();
    await manageCourseDetailPage.verifyCreateDraftSuccess();
    await manageCourseDetailPage.cancelDraftVersion();
    await manageCourseDetailPage.confirmCancelDraftVersion();
  });

  test('@SKL-T20375 @SKL-T20376 Admin setting learning config and verify impact course version', async ({
    configuration,
    loginPage,
    homePage,
    adminDashboardPage,
    manageCourseListPage,
    manageCourseDetailPage,
  }) => {
    const organizationAdmin = configuration.usersLocal.organizationAdminTLI1;
    const regularCourseVersion = configuration.shareCourses.regularCourseImpactVersion;

    // Admin login
    await loginPage.loginWithUsernameAndPassword(organizationAdmin.username, organizationAdmin.password);
    await expect(homePage.userProfileLocator).toBeVisible();
    await adminDashboardPage.accessCoursesMenu();
    await manageCourseListPage.searchCourseName(regularCourseVersion.courseVersions[1].name);
    await manageCourseListPage.viewCourseDetail();

    // Setting learning impact course version
    await manageCourseDetailPage.accessTabGeneralInfo();
    await manageCourseDetailPage.accessMenuLearning();
    await manageCourseDetailPage.enabledFastForwardVideo();
    await manageCourseDetailPage.submitSaveButton();
    await manageCourseDetailPage.verifyCreateDraftSuccess();
    await manageCourseDetailPage.cancelDraftVersion();
    await manageCourseDetailPage.confirmCancelDraftVersion();

    await manageCourseDetailPage.accessTabGeneralInfo();
    await manageCourseDetailPage.accessMenuLearning();
    await manageCourseDetailPage.enabledVideoSpeed();
    await manageCourseDetailPage.submitSaveButton();
    await manageCourseDetailPage.verifyCreateDraftSuccess();
    await manageCourseDetailPage.cancelDraftVersion();
    await manageCourseDetailPage.confirmCancelDraftVersion();

    await manageCourseDetailPage.accessTabGeneralInfo();
    await manageCourseDetailPage.accessMenuLearning();
    await manageCourseDetailPage.enabledCountdownArticle();
    await manageCourseDetailPage.submitSaveButton();
    await manageCourseDetailPage.verifyCreateDraftSuccess();
    await manageCourseDetailPage.cancelDraftVersion();
    await manageCourseDetailPage.confirmCancelDraftVersion();

    await manageCourseDetailPage.accessTabGeneralInfo();
    await manageCourseDetailPage.accessMenuLearning();
    await manageCourseDetailPage.enabledPlayVideoBackground();
    await manageCourseDetailPage.submitSaveButton();
    await manageCourseDetailPage.verifyCreateDraftSuccess();
    await manageCourseDetailPage.cancelDraftVersion();
    await manageCourseDetailPage.confirmCancelDraftVersion();

    await manageCourseDetailPage.accessTabGeneralInfo();
    await manageCourseDetailPage.accessMenuLearning();
    await manageCourseDetailPage.enabledAttentionCheck();
    await manageCourseDetailPage.submitSaveButton();
    await manageCourseDetailPage.verifyCreateDraftSuccess();
    await manageCourseDetailPage.cancelDraftVersion();
    await manageCourseDetailPage.confirmCancelDraftVersion();

    await manageCourseDetailPage.accessTabGeneralInfo();
    await manageCourseDetailPage.accessMenuLearning();
    await manageCourseDetailPage.enabledLearningForceFullscreen();
    await manageCourseDetailPage.submitSaveButton();
    await manageCourseDetailPage.verifyCreateDraftSuccess();
    await manageCourseDetailPage.cancelDraftVersion();
    await manageCourseDetailPage.confirmCancelDraftVersion();
  });

  test.skip('@SKL-T20377 Admin setting course and not impact course version', async ({
    //error 500
    configuration,
    loginPage,
    homePage,
    adminDashboardPage,
    manageCourseListPage,
    manageCourseDetailPage,
  }) => {
    const organizationAdmin = configuration.usersLocal.organizationAdminTLI1;
    const regularCourseVersion = configuration.shareCourses.regularCourseImpactVersion;

    // Admin login
    await loginPage.loginWithUsernameAndPassword(organizationAdmin.username, organizationAdmin.password);
    await expect(homePage.userProfileLocator).toBeVisible();
    await adminDashboardPage.accessCoursesMenu();
    await manageCourseListPage.searchCourseName(regularCourseVersion.courseVersions[1].name);
    await manageCourseListPage.viewCourseDetail();

    // Setting access not impact course version
    await manageCourseDetailPage.accessTabGeneralInfo();
    await manageCourseDetailPage.accessMenuAccessing();
    await manageCourseDetailPage.settingCourseAccessTypePrivate();
    await manageCourseDetailPage.submitSaveButton();
    await manageCourseDetailPage.toastMessageSaveSuccess();
    await manageCourseDetailPage.settingCourseAccessTypePublic();
    await manageCourseDetailPage.submitSaveButton();
    await manageCourseDetailPage.toastMessageSaveSuccess();

    // Setting enrollment not impact course version
    await manageCourseDetailPage.accessTabGeneralInfo();
    await manageCourseDetailPage.accessMenuEnroll();
    await manageCourseDetailPage.enabledSelfEnroll();
    await manageCourseDetailPage.submitSaveButton();
    await manageCourseDetailPage.toastMessageSaveSuccess();

    await manageCourseDetailPage.accessTabGeneralInfo();
    await manageCourseDetailPage.accessMenuEnroll();
    await manageCourseDetailPage.enabledReEnrollDayPassed();
    await manageCourseDetailPage.submitSaveButton();
    await manageCourseDetailPage.toastMessageSaveSuccess();

    await manageCourseDetailPage.accessTabGeneralInfo();
    await manageCourseDetailPage.accessMenuEnroll();
    await manageCourseDetailPage.enabledReEnrollDayExpired();
    await manageCourseDetailPage.submitSaveButton();
    await manageCourseDetailPage.toastMessageSaveSuccess();

    await manageCourseDetailPage.accessTabGeneralInfo();
    await manageCourseDetailPage.accessMenuEnroll();
    await manageCourseDetailPage.startTrainingNow();
    await manageCourseDetailPage.submitSaveButton();
    await manageCourseDetailPage.toastMessageSaveSuccess();
  });

  test('@SKL-T20378 @SKL-T20370 Admin edit course and publish course version and learner enrollment', async ({
    configuration,
    loginPage,
    homePage,
    adminDashboardPage,
    manageCourseListPage,
    manageCourseDetailPage,
    searchResultPage,
    courseDetailPage,
    learningPage,
    regularEnrollmentPage,
    page,
  }) => {
    const organizationAdmin = configuration.usersLocal.organizationAdminTLI1;
    const regularCourseVersion = configuration.shareCourses.regularCourseVersion;
    const userEnrollVersionTwo = configuration.shareUsers.userMember1TLI;
    const userEnrollVersionThree = configuration.shareUsers.userMember2TLI;

    // Admin login
    await loginPage.loginWithUsernameAndPassword(organizationAdmin.username, organizationAdmin.password);
    await expect(homePage.userProfileLocator).toBeVisible();
    await adminDashboardPage.accessCoursesMenu();
    await manageCourseListPage.searchCourseName(regularCourseVersion.courseVersions[1].name);
    await manageCourseListPage.viewCourseDetail();
    await manageCourseDetailPage.accessCourseGeneralDetail(regularCourseVersion.courseVersions[1].name);

    // Admin edit course regular and new version 2
    await manageCourseDetailPage.editCourseName(regularCourseVersion.courseVersions[2].name);
    await manageCourseDetailPage.submitSaveButton();
    await manageCourseDetailPage.accessTabCurriculum();
    await manageCourseDetailPage.editCurriculum();
    await manageCourseDetailPage.deleteCourseContent(2);
    await manageCourseDetailPage.confirmDeleteContent();
    await manageCourseDetailPage.saveCourseContent();
    await manageCourseDetailPage.publishNewVersioning();
    await homePage.logout();

    // Learner enroll course regular version 2
    await page.reload();
    await loginPage.loginWithUsernameAndPassword(userEnrollVersionTwo.username, userEnrollVersionTwo.password);
    await expect(homePage.userProfileLocator).toBeVisible();
    await homePage.searchCourse(regularCourseVersion.courseVersions[2].name);
    await searchResultPage.clickCourseCard(
      false,
      regularCourseVersion.objective,
      regularCourseVersion.courseVersions[2].name,
    );
    await courseDetailPage.clickSelfEnroll();
    await learningPage.clickBackToMyCourse();
    await homePage.logout();

    // Admin login
    await page.reload();
    await loginPage.loginWithUsernameAndPassword(organizationAdmin.username, organizationAdmin.password);
    await expect(homePage.userProfileLocator).toBeVisible();
    await adminDashboardPage.accessCoursesMenu();
    await manageCourseListPage.searchCourseName(regularCourseVersion.courseVersions[2].name);
    await manageCourseListPage.viewCourseDetail();
    await manageCourseDetailPage.accessCourseGeneralDetail(regularCourseVersion.courseVersions[2].name);

    // Admin edit course regular and new version 3
    await manageCourseDetailPage.editCourseName(regularCourseVersion.courseVersions[3].name);
    await manageCourseDetailPage.submitSaveButton();
    await manageCourseDetailPage.accessTabCurriculum();
    await manageCourseDetailPage.editCurriculum();
    await manageCourseDetailPage.deleteCourseContent(1);
    await manageCourseDetailPage.confirmDeleteContent();
    await manageCourseDetailPage.saveCourseContent();
    await manageCourseDetailPage.publishNewVersioning();
    await homePage.logout();

    // Learner enroll course regular version 3
    await page.reload();
    await loginPage.loginWithUsernameAndPassword(userEnrollVersionThree.username, userEnrollVersionThree.password);
    await expect(homePage.userProfileLocator).toBeVisible();
    await homePage.searchCourse(regularCourseVersion.courseVersions[3].name);
    await searchResultPage.clickCourseCard(
      false,
      regularCourseVersion.objective,
      regularCourseVersion.courseVersions[3].name,
    );
    await courseDetailPage.clickSelfEnroll();
    await learningPage.clickConfirmCompleteLearning();
    await learningPage.clickBackToMyCourse();
    await homePage.logout();

    // Admin verify enrollment course version of learner
    await page.reload();
    await loginPage.loginWithUsernameAndPassword(organizationAdmin.username, organizationAdmin.password);
    await expect(homePage.userProfileLocator).toBeVisible();
    await page.waitForTimeout(5000);
    await adminDashboardPage.accessManageRegularEnrollment();
    await regularEnrollmentPage.clickEnrollmentLearningTab();
    await regularEnrollmentPage.searchFullName(userEnrollVersionTwo.firstname);
    await regularEnrollmentPage.clickEnrollmentDetail();
    await regularEnrollmentPage.verifyEnrollmentCourseVersion('2');
    await adminDashboardPage.accessEnrollmentRegularMenu();
    await regularEnrollmentPage.clickEnrollmentLearningTab();
    await regularEnrollmentPage.filterStatus('เรียนจบแล้ว');
    await regularEnrollmentPage.searchFullName(userEnrollVersionThree.firstname);
    await regularEnrollmentPage.clickEnrollmentDetail();
    await regularEnrollmentPage.verifyEnrollmentCourseVersion('3');
  });
});

test.describe('Learner - Verify course versioning', () => {
  test('Learner verify enrollment course version', async ({
    configuration,
    loginPage,
    homePage,
    adminDashboardPage,
    manageCourseListPage,
    manageCourseDetailPage,
    searchResultPage,
    courseDetailPage,
    learningPage,
  }) => {
    const organizationAdmin = configuration.usersLocal.organizationAdminTLI1;
    const regularCourseVersion = configuration.shareCourses.regularCourseVersion;
    const userEnrollVersionTwo = configuration.shareUsers.userMember1TLI;

    // Learner verify version 1
    await loginPage.loginWithUsernameAndPassword(userEnrollVersionTwo.username, userEnrollVersionTwo.password);
    await expect(homePage.userProfileLocator).toBeVisible();
    await homePage.searchCourse(regularCourseVersion.courseVersions[1].name);
    await searchResultPage.clickCourseCard(
      false,
      regularCourseVersion.objective,
      regularCourseVersion.courseVersions[1].name,
    );
    await courseDetailPage.accessCurriculumDetail();
    await courseDetailPage.verifyCurriculumDetail(['บทความที่:1', 'บทความที่:2', 'บทความที่:3']);
    await homePage.logout();

    // Admin login
    await loginPage.loginWithUsernameAndPassword(organizationAdmin.username, organizationAdmin.password);
    await expect(homePage.userProfileLocator).toBeVisible();
    await adminDashboardPage.accessCoursesMenu();
    await manageCourseListPage.searchCourseName(regularCourseVersion.courseVersions[1].name);
    await manageCourseListPage.viewCourseDetail();
    await manageCourseDetailPage.accessCourseGeneralDetail(regularCourseVersion.courseVersions[1].name);

    // Admin edit course regular and new version 2
    await manageCourseDetailPage.editCourseName(regularCourseVersion.courseVersions[2].name);
    await manageCourseDetailPage.submitSaveButton();
    await manageCourseDetailPage.accessTabCurriculum();
    await manageCourseDetailPage.editCurriculum();
    await manageCourseDetailPage.deleteCourseContent(2);
    await manageCourseDetailPage.confirmDeleteContent();
    await manageCourseDetailPage.saveCourseContent();
    await manageCourseDetailPage.publishNewVersioning();
    await homePage.logout();

    // Learner enroll course regular version 2
    await loginPage.loginWithUsernameAndPassword(userEnrollVersionTwo.username, userEnrollVersionTwo.password);
    await expect(homePage.userProfileLocator).toBeVisible();
    await homePage.searchCourse(regularCourseVersion.courseVersions[2].name);
    await searchResultPage.clickCourseCard(
      false,
      regularCourseVersion.objective,
      regularCourseVersion.courseVersions[2].name,
    );
    await courseDetailPage.accessCurriculumDetail();
    await courseDetailPage.verifyCurriculumDetail(['บทความที่:1', 'บทความที่:3']);
    await courseDetailPage.clickSelfEnroll();
    await learningPage.verifyChapterContentInLearning(['บทความที่:1', 'บทความที่:3']);
    await learningPage.clickBackToMyCourse();
    await homePage.logout();
  });
});
