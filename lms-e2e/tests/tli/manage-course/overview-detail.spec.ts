import { readFile, utils, writeFile } from 'xlsx';
import { expect } from '@playwright/test';
import { test } from '../../../fixtures/default-fixture';
import { CourseVersionStatus } from '../../../../shared/repositories/lms/constants/enums/course-version.enum';
import { ObjectiveCourse } from '../../../elements/pages/backoffice/create-course-elements';

test.beforeEach(async ({ courseVersionsRepo, enrollmentsRepo, instructorRepo, configuration, coursesRepo }) => {
  const regularCourseVersions = configuration.shareCourses.regularCourseVersion.courseVersions[1];
  const impactCourseVersion = configuration.shareCourses.regularCourseImpactVersion.courseVersions[2];
  const oicCourseName = '[Automate][TEMP] Admin create OIC course';
  const oicCourseCode = 'AUTOMATE_OIC_TEMP';

  const courseVersions = [
    configuration.shareCourses.regularCourseVersion.courseVersions[2],
    configuration.shareCourses.regularCourseVersion.courseVersions[3],
    { name: oicCourseName },
  ];

  const usersEnrollCourseVersion = [configuration.shareUsers.userMember1TLI, configuration.shareUsers.userMember2TLI];

  // Clean up test instructor data
  await instructorRepo.deleteByEmail('<EMAIL>');

  for (const versions of courseVersions) {
    await courseVersionsRepo.deleteByCourseName(versions.name);
  }

  await coursesRepo.deleteCourseByCode(oicCourseCode);

  await courseVersionsRepo.updateByCourseVersion(regularCourseVersions.id, CourseVersionStatus.published);

  await courseVersionsRepo.deleteByCourseStatus(impactCourseVersion.courseId, CourseVersionStatus.draft);

  for (const users of usersEnrollCourseVersion) {
    await enrollmentsRepo.deleteAllEnrollmentsForUser(users.guid);
  }
});

test.afterEach(async ({ courseVersionsRepo, enrollmentsRepo, instructorRepo, configuration, coursesRepo }) => {
  const regularCourseVersions = configuration.shareCourses.regularCourseVersion.courseVersions[1];
  const impactCourseVersion = configuration.shareCourses.regularCourseImpactVersion.courseVersions[2];
  const oicCourseName = '[Automate][TEMP] Admin create OIC course';
  const oicCourseCode = 'AUTOMATE_OIC_TEMP';

  const regularCourseNewVersion = [
    configuration.shareCourses.regularCourseVersion.courseVersions[2],
    configuration.shareCourses.regularCourseVersion.courseVersions[3],
    { name: oicCourseName },
  ];

  const usersEnrollCourseVersion = [configuration.shareUsers.userMember1TLI, configuration.shareUsers.userMember2TLI];

  // Clean up test instructor data
  await instructorRepo.deleteByEmail('<EMAIL>');

  for (const versions of regularCourseNewVersion) {
    await courseVersionsRepo.deleteByCourseName(versions.name);
  }

  await coursesRepo.deleteCourseByCode(oicCourseCode);

  await courseVersionsRepo.updateByCourseVersion(regularCourseVersions.id, CourseVersionStatus.published);

  await courseVersionsRepo.deleteByCourseStatus(impactCourseVersion.courseId, CourseVersionStatus.draft);

  for (const users of usersEnrollCourseVersion) {
    await enrollmentsRepo.deleteAllEnrollmentsForUser(users.guid);
  }
});

test.describe('Admin - Course Management', () => {
  test('@SKL-T20189 Admin เข้าถึงหน้าหลักสูตรทั้งหมด, แสดงหน้าตารางรายการหลักสูตรทั้งหมด', async ({
    configuration,
    loginPage,
    manageCourseListPage,
    adminDashboardPage,
  }) => {
    const adminTLI = configuration.shareUsers.adminRequestDocument;

    // Admin login
    await loginPage.loginWithUsernameAndPassword(adminTLI.username, adminTLI.password);
    await adminDashboardPage.accessCoursesMenu();
    await expect(manageCourseListPage.courseRowsLocator.first()).toBeVisible();
    await expect(manageCourseListPage.searchInputLocator).toBeVisible();
    await expect(manageCourseListPage.searchSubmitBtnLocator).toBeVisible();
    await expect(manageCourseListPage.createCourseButtonLocator).toBeVisible();
  });
  test('@SKL-T20194 Admin สร้างหลักสูตรแต่กรอกข้อมูลเนื้อหาเบื้องต้นรูปแบบไม่ถูกต้อง, แสดง error ไม่สามารถสร้างได้', async ({
    configuration,
    loginPage,
    manageCourseListPage,
    adminDashboardPage,
    page,
  }) => {
    const adminTLI = configuration.shareUsers.adminRequestDocument;
    const courseCodeIncorrectFormat = 'course code!';
    const courseNameWithLeadingSpace = ' Course Name with Leading Space';
    const courseNameWithTrailingSpace = 'Course Name with Trailing Space ';
    const courseNameWithMultipleSpace = 'Course Name with  Multiple Space';
    const courseNameTooLong =
      'courseNameCourseNameCourseNameCourseNameCourseNameCourseNameCourseNameCourseNameCourseNameCourseNamecourseNameCourseNameCourseNameCourseNameCourseNameCourseNameCourseNameCourseNameCourseNameCourseNamecourseNameCourseNameCourseNameCourseNameCourseNameCourseNameCourseNameCourseNameCourseNameCourseName1';

    // Admin login
    await loginPage.loginWithUsernameAndPassword(adminTLI.username, adminTLI.password);
    await adminDashboardPage.accessCoursesMenu();
    await expect(manageCourseListPage.courseRowsLocator.first()).toBeVisible();
    await expect(manageCourseListPage.searchInputLocator).toBeVisible();
    await expect(manageCourseListPage.searchSubmitBtnLocator).toBeVisible();
    await expect(manageCourseListPage.createCourseButtonLocator).toBeVisible();
    await manageCourseListPage.createCourse();
    //กรณีว่าง
    await manageCourseListPage.createCourseForm.submitCreateCourseButton();
    //validate error message when empty
    await manageCourseListPage.createCourseForm.validateObjectiveErrorMessage('กรุณาเลือกวัตถุประสงค์');
    await manageCourseListPage.createCourseForm.validateCourseCodeErrorMessage('กรุณากรอกรหัสเนื้อหา');
    await manageCourseListPage.createCourseForm.validateCourseNameErrorMessage('กรุณากรอกชื่อเนื้อหา');

    await manageCourseListPage.createCourseForm.selectObjectiveCourse(ObjectiveCourse.OIC);

    await manageCourseListPage.createCourseForm.submitCreateCourseButton();
    //validate error message when empty and objective course is OIC
    await manageCourseListPage.createCourseForm.validateCourseCodeErrorMessage('กรุณากรอกรหัสเนื้อหา');
    await manageCourseListPage.createCourseForm.validateCourseNameErrorMessage('กรุณากรอกชื่อเนื้อหา');
    await manageCourseListPage.createCourseForm.clearForm();
    await manageCourseListPage.createCourseForm.fillCourseCode(courseCodeIncorrectFormat);
    await manageCourseListPage.createCourseForm.submitCreateCourseButton();
    await manageCourseListPage.createCourseForm.validateCourseCodeErrorMessage(
      'กรุณากรอก A-Z หรือ 0-9 หรือ _ เท่านั้น',
    );

    await manageCourseListPage.createCourseForm.clearForm();

    await manageCourseListPage.createCourseForm.fillCourseName(courseNameWithLeadingSpace);
    await manageCourseListPage.createCourseForm.submitCreateCourseButton();
    await manageCourseListPage.createCourseForm.validateCourseNameErrorMessage(
      'ชื่อเนื้อหาไม่สามารถเริ่มต้นด้วยเว้นวรรค',
    );

    await manageCourseListPage.createCourseForm.clearForm();
    await manageCourseListPage.createCourseForm.fillCourseName(courseNameWithTrailingSpace);
    await manageCourseListPage.createCourseForm.submitCreateCourseButton();
    await manageCourseListPage.createCourseForm.validateCourseNameErrorMessage(
      'ชื่อเนื้อหาไม่สามารถลงท้ายด้วยเว้นวรรค',
    );

    await manageCourseListPage.createCourseForm.clearForm();
    await manageCourseListPage.createCourseForm.fillCourseName(courseNameWithMultipleSpace);
    await manageCourseListPage.createCourseForm.submitCreateCourseButton();
    await manageCourseListPage.createCourseForm.validateCourseNameErrorMessage(
      'ชื่อเนื้อหาไม่สามารถมีเว้นวรรคติดกันได้',
    );

    await manageCourseListPage.createCourseForm.clearForm();
    await manageCourseListPage.createCourseForm.fillCourseName(courseNameTooLong);
    await manageCourseListPage.createCourseForm.submitCreateCourseButton();
    await manageCourseListPage.createCourseForm.validateCourseNameErrorMessage(
      'ชื่อเนื้อหาควรมีความยาวไม่เกิน 300 ตัวอักษร',
    );
  });
  test('@SKL-T20221 Admin แก้ไขข้อมูลภาพรวมของหลักสูตรการเรียนทั่วไป กรณีกรอกข้อมูลไม่ถูกต้อง, แสดง error และไม่สามารถบันทึกได้', async ({
    configuration,
    loginPage,
    homePage,
    adminDashboardPage,
    manageCourseListPage,
    manageCourseDetailPage,
    page,
  }) => {
    const organizationAdmin = configuration.usersLocal.organizationAdminTLI1;
    const regularCourse = configuration.shareCourses.regularCourseVersion;

    // Admin login
    await loginPage.loginWithUsernameAndPassword(organizationAdmin.username, organizationAdmin.password);
    await expect(homePage.userProfileLocator).toBeVisible();
    await adminDashboardPage.accessCoursesMenu();
    await manageCourseListPage.searchCourseName(regularCourse.courseVersions[1].name);
    await manageCourseListPage.viewCourseDetail();

    // Edit course overview with incorrect data
    await manageCourseDetailPage.accessCourseGeneralDetail(regularCourse.courseVersions[1].name);
    await manageCourseDetailPage.editCourseName(''); // Empty name should cause error

    // should show กรอกชื่อเนื้อหา err msg for the ชื่อหลักสูตร input
    // Empty name
    await expect(manageCourseDetailPage.courseNameErrorLocator).toBeVisible();

    // Leading space
    await manageCourseDetailPage.editCourseName(' ชื่อหลักสูตร');
    await expect(manageCourseDetailPage.courseNameLeadingSpaceErrorLocator).toBeVisible();

    // Trailing space
    await manageCourseDetailPage.editCourseName('ชื่อหลักสูตร ');
    await expect(manageCourseDetailPage.courseNameTrailingSpaceErrorLocator).toBeVisible();

    // Consecutive spaces
    await manageCourseDetailPage.editCourseName('ชื่อ  หลักสูตร');
    await expect(manageCourseDetailPage.courseNameConsecutiveSpaceErrorLocator).toBeVisible();

    // Too long
    await manageCourseDetailPage.editCourseName('A'.repeat(301));
    await expect(manageCourseDetailPage.courseNameTooLongErrorLocator).toBeVisible();

    const invalidFilePath = 'shared/images/invalid_image.jpg';
    await manageCourseDetailPage.thumbnailUploadInputLocator.setInputFiles(invalidFilePath);
    await manageCourseDetailPage.saveButtonLocator.click();
    await expect(manageCourseDetailPage.thumbnailUploadErrorLocator).toBeVisible();
    //upload thumbnail  (รูปแบบไฟล์ไม่ใช่ .png, .jpg, .jpeg,มีขนาดไฟล์เกิน 30 MB)('กรุณาเลือกอัปโหลดรูปแบบไฟล์ .png, .jpg, .jpeg และขนาดไม่เกิน 30 MB');

    // '@SKL-T20219 Admin แก้ไขข้อมูลภาพรวมของหลักสูตรการเรียนทั่วไป กรณีกรอกข้อมูลถูกต้อง, สามารถแก้ไขได้และบันทึกสำเร็จ'
    await manageCourseDetailPage.editCourseName('[AUTOMATE] Admin Test manage course draft version - Valid');
    await manageCourseDetailPage.fillCourseDescription('รายละเอียดเนื้อหาทดสอบสำหรับหลักสูตรการเรียนทั่วไป');
    const validFilePath = 'shared/images/valid_image.jpg';
    await manageCourseDetailPage.thumbnailUploadInputLocator.setInputFiles(validFilePath);
    await manageCourseDetailPage.submitSaveButton();
    await manageCourseDetailPage.verifyCreateDraftSuccess();
  });

  // *CREATE AND DELETE DRAFT VERSION 1
  test('@SKL-T20252 Admin ลบหลักสูตรแบบร่างเวอร์ชัน 1 ที่สร้างบนระบบ, สามารถลบหลักสูตรได้', async ({
    configuration,
    loginPage,
    manageCourseListPage,
    organizationAdminDashboardPage,
    coursesRepo,
    courseVersionsRepo,
    manageCourseDetailPage,
  }) => {
    const adminTLI = configuration.shareUsers.adminRequestDocument;
    const courseName = '[Automate] Admin Test manage course delete draft version';
    const courseCode = 'DELETE_DRAFT';
    // TODO: MOVE TO BEFORE/ AFTER HOOK
    await courseVersionsRepo.deleteByCourseName(courseName);
    await coursesRepo.deleteCourseByCode(courseCode);
    // Admin login
    await loginPage.loginWithUsernameAndPassword(adminTLI.username, adminTLI.password);
    await organizationAdminDashboardPage.accessManageCourseList();
    await expect(manageCourseListPage.courseRowsLocator.first()).toBeVisible();
    await expect(manageCourseListPage.searchInputLocator).toBeVisible();
    await expect(manageCourseListPage.searchSubmitBtnLocator).toBeVisible();
    await expect(manageCourseListPage.createCourseButtonLocator).toBeVisible();
    await manageCourseListPage.createCourse();
    await manageCourseListPage.createCourseForm.selectObjectiveCourse(ObjectiveCourse.Regular);
    await manageCourseListPage.createCourseForm.fillCourseCode(courseCode);
    await manageCourseListPage.createCourseForm.fillCourseName(courseName);
    await manageCourseListPage.createCourseForm.submitCreateCourseButton();
    await expect(manageCourseListPage.toastMsg.validateMsgLocator).toContainText('สร้างหลักสูตรสำเร็จ');
    //ตรวจสอบ เวอร์ชัน 1 (ร่าง)
    await manageCourseDetailPage.deleteContentButtonLocator.click();
    //ยืนยันการลบ
    await manageCourseDetailPage.confirmDeleteCourse();
    await expect(manageCourseListPage.toastMsg.validateMsgLocator).toContainText('ลบหลักสูตรสำเร็จ');
  });
  test('@SKL-T20249 Admin นำเข้าข้อมูลสำหรับออกรายงาน คปภ. ของหลักสูตรการอบรมวิชาชีพประกัน (OIC) และกรอกรูปแบบข้อมูลไม่ถูกต้อง, แสดง error และไม่สามารถบันทึกได้', async ({
    //NOTE: แก้ไขหลังจากข้อนี้หรือควรสร้างคอร์สไว้ แล้วไปแก้ไขโดยเช็คว่าถ้าเลือก1 ให้เลือก2 ถ้าเลือก2 ให้เลือก1
    configuration,
    loginPage,
    manageCourseListPage,
    organizationAdminDashboardPage,
    coursesRepo,
    courseVersionsRepo,
    manageCourseDetailPage,
    page,
  }) => {
    const adminTLI = configuration.shareUsers.adminRequestDocument;
    const courseName = '[Automate] Admin Test manage course delete draft version';
    const courseCode = 'DELETE_DRAFT';
    await courseVersionsRepo.deleteByCourseName(courseName);
    await coursesRepo.deleteCourseByCode(courseCode);
    // Admin login
    await loginPage.loginWithUsernameAndPassword(adminTLI.username, adminTLI.password);
    await organizationAdminDashboardPage.accessManageCourseList();
    await expect(manageCourseListPage.courseRowsLocator.first()).toBeVisible();
    await expect(manageCourseListPage.searchInputLocator).toBeVisible();
    await expect(manageCourseListPage.searchSubmitBtnLocator).toBeVisible();
    await expect(manageCourseListPage.createCourseButtonLocator).toBeVisible();
    await manageCourseListPage.createCourse();
    await manageCourseListPage.createCourseForm.selectObjectiveCourse(ObjectiveCourse.OIC);
    await manageCourseListPage.createCourseForm.fillCourseCode(courseCode);
    await manageCourseListPage.createCourseForm.fillCourseName(courseName);
    await manageCourseListPage.createCourseForm.submitCreateCourseButton();
    await expect(manageCourseListPage.toastMsg.validateMsgLocator).toContainText('สร้างหลักสูตรสำเร็จ');

    //กดเข้าเมนูข้อมูลออกรายงาน
    await manageCourseDetailPage.accessMenuOicReport();

    //ดาวน์โหลดไฟล์ตัวอย่าง
    const templateFilePath = await manageCourseDetailPage.downloadOicReportTemplate();

    //กดปุ่มนำเข้าข้อมูล
    await manageCourseDetailPage.clickImportDataButton();

    //อัปโหลดข้อมูล empty file (ไฟล์ที่ไม่มีข้อมูล)
    const emptyFilePath = 'oic-report-empty.xlsx';
    const emptyWorkbook = readFile(templateFilePath);
    // Keep only headers, remove all data rows
    const emptyWorksheet = emptyWorkbook.Sheets['Sheet1'];
    const emptyData = utils.sheet_to_json(emptyWorksheet, { header: 1 }) as any[][];
    const headerOnly = [emptyData[0]]; // Keep only the first row (headers)
    const newEmptyWorksheet = utils.aoa_to_sheet(headerOnly);
    emptyWorkbook.Sheets['Sheet1'] = newEmptyWorksheet;
    writeFile(emptyWorkbook, emptyFilePath);
    await manageCourseDetailPage.uploadFile(emptyFilePath);

    //กดยืนยัน
    await manageCourseDetailPage.confirmImport();

    //validate error message when empty file
    await expect(manageCourseDetailPage.emptyFileErrorMessageLocator).toContainText(
      'ไม่พบแถวข้อมูล กรุณาลองใหม่อีกครั้ง',
    );

    //อัปโหลดข้อมูล data ไม่ครบ (ข้อมูลไม่ครบถ้วน)
    const incompleteFilePath = 'oic-report-incomplete.xlsx';
    const incompleteWorkbook = readFile(templateFilePath);
    const incompleteWorksheet = incompleteWorkbook.Sheets['Sheet1'];
    const incompleteData = utils.sheet_to_json(incompleteWorksheet, { header: 1 }) as any[][];
    // Add incomplete data row (missing required fields)
    const incompleteRow = ['1234567890123', '', '<EMAIL>', '', '', '', '']; // Missing full_name, license_type, training_date, course_code, status
    const incompleteDataWithRow = [incompleteData[0], incompleteRow];
    const newIncompleteWorksheet = utils.aoa_to_sheet(incompleteDataWithRow);
    incompleteWorkbook.Sheets['Sheet1'] = newIncompleteWorksheet;
    writeFile(incompleteWorkbook, incompleteFilePath);
    await manageCourseDetailPage.uploadFile(incompleteFilePath);

    //กดยืนยัน
    await manageCourseDetailPage.confirmImport();

    //validate error message when incomplete data
    await expect(manageCourseDetailPage.incompleteDataErrorMessageLocator).toBeVisible();
  });
  test('@SKL-T20248 Admin นำเข้าข้อมูลสำหรับออกรายงาน คปภ. ของหลักสูตรการอบรมวิชาชีพประกัน (OIC) และกรอกรูปแบบข้อมูลถูกต้อง, สามารถแก้ไขได้และบันทึกสำเร็จ', async ({
    //NOTE: แก้ไขหลังจากข้อนี้หรือควรสร้างคอร์สไว้ แล้วไปแก้ไขโดยเช็คว่าถ้าเลือก1 ให้เลือก2 ถ้าเลือก2 ให้เลือก1
    configuration,
    loginPage,
    manageCourseListPage,
    organizationAdminDashboardPage,
    coursesRepo,
    courseVersionsRepo,
    manageCourseDetailPage,
  }) => {
    const adminTLI = configuration.shareUsers.adminRequestDocument;
    const courseName = '[Automate] Admin Test manage course delete draft version';
    const courseCode = 'DELETE_DRAFT';
    await courseVersionsRepo.deleteByCourseName(courseName);
    await coursesRepo.deleteCourseByCode(courseCode);
    // Admin login
    await loginPage.loginWithUsernameAndPassword(adminTLI.username, adminTLI.password);
    await organizationAdminDashboardPage.accessManageCourseList();
    await expect(manageCourseListPage.courseRowsLocator.first()).toBeVisible();
    await expect(manageCourseListPage.searchInputLocator).toBeVisible();
    await expect(manageCourseListPage.searchSubmitBtnLocator).toBeVisible();
    await expect(manageCourseListPage.createCourseButtonLocator).toBeVisible();
    await manageCourseListPage.createCourse();
    await manageCourseListPage.createCourseForm.selectObjectiveCourse(ObjectiveCourse.OIC);
    await manageCourseListPage.createCourseForm.fillCourseCode(courseCode);
    await manageCourseListPage.createCourseForm.fillCourseName(courseName);
    await manageCourseListPage.createCourseForm.submitCreateCourseButton();
    await expect(manageCourseListPage.toastMsg.validateMsgLocator).toContainText('สร้างหลักสูตรสำเร็จ');

    //กดเข้าเมนูข้อมูลออกรายงาน
    await manageCourseDetailPage.accessMenuOicReport();

    //ดาวน์โหลดไฟล์ตัวอย่าง
    const templateFilePath = await manageCourseDetailPage.downloadOicReportTemplate();

    //กดปุ่มนำเข้าข้อมูล
    await manageCourseDetailPage.clickImportDataButton();

    //อัปโหลดข้อมูล data ครบ (ข้อมูลครบถ้วน)
    const validFilePath = 'oic-report-valid.xlsx';
    const validWorkbook = readFile(templateFilePath);
    const validWorksheet = validWorkbook.Sheets['Sheet1'];
    const validData = utils.sheet_to_json(validWorksheet, { header: 1 }) as any[][];
    // Add complete valid data row
    const validRow = [
      '1234567890123', // citizen_id
      'John Doe', // full_name
      '<EMAIL>', // email
      'OIC-001', // license_type
      '01/01/2024', // training_date
      'COURSE001', // course_code
      'PASSED', // status
    ];
    const validDataWithRow = [validData[0], validRow];
    const newValidWorksheet = utils.aoa_to_sheet(validDataWithRow);
    validWorkbook.Sheets['Sheet1'] = newValidWorksheet;
    writeFile(validWorkbook, validFilePath);
    await manageCourseDetailPage.uploadFile(validFilePath);

    await manageCourseDetailPage.confirmImport();

    //validate toast message "นำเข้าข้อมูลสำเร็จ"
    await expect(manageCourseDetailPage.successImportMessageLocator).toContainText('นำเข้าข้อมูลสำเร็จ');
  });

  // *ENROLLMENT MENU
  test('@SKL-T20245 Admin แก้ไขเปิดใช้งานตั้งค่าเปิดลงทะเบียนซ้ำเมื่อหลักสูตรหมดอายุของหลักสูตรการเรียนทั่วไป และกรอกรูปแบบข้อมูลไม่ถูกต้อง, แสดง error และไม่สามารถบันทึกได้', async ({
    //NOTE: แก้ไขหลังจากข้อนี้หรือควรสร้างคอร์สไว้ แล้วไปแก้ไขโดยเช็คว่าถ้าเลือก1 ให้เลือก2 ถ้าเลือก2 ให้เลือก1
    configuration,
    loginPage,
    manageCourseListPage,
    organizationAdminDashboardPage,
    coursesRepo,
    courseVersionsRepo,
    manageCourseDetailPage,
    page,
  }) => {
    const adminTLI = configuration.shareUsers.adminRequestDocument;
    const courseName = '[Automate] Admin Test manage course delete draft version';
    const courseCode = 'DELETE_DRAFT';
    await courseVersionsRepo.deleteByCourseName(courseName);
    await coursesRepo.deleteCourseByCode(courseCode);
    // Admin login
    await loginPage.loginWithUsernameAndPassword(adminTLI.username, adminTLI.password);
    await organizationAdminDashboardPage.accessManageCourseList();
    await expect(manageCourseListPage.courseRowsLocator.first()).toBeVisible();
    await expect(manageCourseListPage.searchInputLocator).toBeVisible();
    await expect(manageCourseListPage.searchSubmitBtnLocator).toBeVisible();
    await expect(manageCourseListPage.createCourseButtonLocator).toBeVisible();
    await manageCourseListPage.createCourse();
    await manageCourseListPage.createCourseForm.selectObjectiveCourse(ObjectiveCourse.Regular);
    await manageCourseListPage.createCourseForm.fillCourseCode(courseCode);
    await manageCourseListPage.createCourseForm.fillCourseName(courseName);
    await manageCourseListPage.createCourseForm.submitCreateCourseButton();
    await expect(manageCourseListPage.toastMsg.validateMsgLocator).toContainText('สร้างหลักสูตรสำเร็จ');

    await manageCourseDetailPage.accessMenuEnroll();

    await manageCourseDetailPage.editReEnrollmentExpireButtonLocator.click();
    await manageCourseDetailPage.toggleReEnrollWhenExpireLocator.click();
    await manageCourseDetailPage.reEnrollExpireDayInputLocator.clear();
    await manageCourseDetailPage.saveButtonLocator.click();

    await expect(manageCourseDetailPage.reEnrollExpireDayEmptyErrorLocator).toBeVisible();

    await manageCourseDetailPage.reEnrollExpireDayInputLocator.fill('0');
    await manageCourseDetailPage.saveButtonLocator.click();
    await expect(manageCourseDetailPage.reEnrollExpireDayZeroErrorLocator).toBeVisible();

    await manageCourseDetailPage.discardButtonLocator.click();
    await manageCourseDetailPage.confirmDiscard();

    // @SKL-T20244 Admin แก้ไขเปิดใช้งานตั้งค่าเปิดลงทะเบียนซ้ำเมื่อหลักสูตรหมดอายุของหลักสูตรการเรียนทั่วไป และกรอกรูปแบบข้อมูลถูกต้อง, สามารถแก้ไขได้และบันทึกสำเร็จ
    //กดปุ่ม "แก้ไข" ของตั้งค่าเปิดลงทะเบียนซ้ำเมื่อหลักสูตรหมดอายุ
    await manageCourseDetailPage.editReEnrollmentExpireButtonLocator.click();

    //เปิดใช้งาน
    await manageCourseDetailPage.toggleReEnrollWhenExpireLocator.click();

    //กรอกจำนวนวันที่ถูกต้อง
    await manageCourseDetailPage.fillReEnrollmentExpireDayInput('30');

    //กดปุ่มบันทึก
    await manageCourseDetailPage.saveButtonLocator.click();

    //validate toast message "บันทึกข้อมูลสำเร็จ" และแสดงข้อมูลถูกต้อง
    await expect(manageCourseDetailPage.saveSuccessToastLocator.first()).toBeVisible();

    // @SKL-T20246 Admin แก้ไขปิดใช้งานตั้งค่าเปิดลงทะเบียนซ้ำเมื่อหลักสูตรหมดอายุของหลักสูตรการเรียนทั่วไป, สามารถแก้ไขได้และบันทึกสำเร็จ
    //กดปุ่ม "แก้ไข" ของตั้งค่าเปิดลงทะเบียนซ้ำเมื่อหลักสูตรหมดอายุ
    await manageCourseDetailPage.editReEnrollmentExpireButtonLocator.click();

    //ปิดใช้งาน
    await manageCourseDetailPage.toggleReEnrollWhenExpireLocator.click();

    //กดปุ่มบันทึก
    await manageCourseDetailPage.saveButtonLocator.click();

    //validate toast message "บันทึกข้อมูลสำเร็จ" และแสดงข้อมูลถูกต้อง
    await expect(manageCourseDetailPage.saveSuccessToastLocator.first()).toBeVisible();

    // @SKL-T20243 Admin แก้ไขวิธีการเริ่มเรียนหลักสูตรของหลักสูตรการเรียนทั่วไป เป็นเริ่มทันทีที่มอบหมาย/ลงเรียน, สามารถแก้ไขได้และบันทึกสำเร็จ
    //กดปุ่ม "แก้ไข" ของตั้งค่าการเริ่มหลักสูตร
    await manageCourseDetailPage.editCourseStartButtonLocator.click();

    //เลือก เริ่มทันทีที่มอบหมาย / ลงเรียน
    await manageCourseDetailPage.selectEnrollImmediateLocator.click();

    //กดปุ่มบันทึก
    await manageCourseDetailPage.saveButtonLocator.click();

    //validate toast message "บันทึกข้อมูลสำเร็จ" และแสดงข้อมูลถูกต้อง
    await expect(manageCourseDetailPage.saveSuccessToastLocator.first()).toBeVisible();

    // @SKL-T20240 Admin แก้ไขวิธีการเริ่มเรียนหลักสูตรของหลักสูตรการเรียนทั่วไป เป็นเริ่มตามรอบที่กำหนด, สามารถแก้ไขได้และบันทึกสำเร็จ
    //กดปุ่ม "แก้ไข" ของตั้งค่าการเริ่มหลักสูตร
    await manageCourseDetailPage.editCourseStartButtonLocator.click();

    //เลือก เริ่มตามรอบที่กำหนด
    await manageCourseDetailPage.selectEnrollWithRoundLocator.click();

    //กดปุ่มบันทึก
    await manageCourseDetailPage.saveButtonLocator.click();

    //validate toast message "บันทึกข้อมูลสำเร็จ" และแสดงข้อมูลถูกต้อง
    await expect(manageCourseDetailPage.saveSuccessToastLocator.first()).toBeVisible();

    // @SKL-T20238 Admin แก้ไขเปิดใช้งานตั้งค่าเปิดลงทะเบียนซ้ำเมื่อผ่านหลักสูตรของหลักสูตรการเรียนทั่วไป และกรอกรูปแบบข้อมูลไม่ถูกต้อง, แสดง error และไม่สามารถบันทึกได้
    //กดปุ่ม "แก้ไข" ของตั้งค่าเปิดลงทะเบียนซ้ำเมื่อผ่านหลักสูตร
    await manageCourseDetailPage.editReEnrollmentPassButtonLocator.click();

    //เปิดใช้งาน
    await manageCourseDetailPage.toggleReEnrollWhenPassLocator.click();

    //validate error message when empty
    await manageCourseDetailPage.reEnrollPassDayInputLocator.clear();
    await manageCourseDetailPage.saveButtonLocator.click();
    await expect(manageCourseDetailPage.reEnrollPassDayEmptyErrorLocator).toBeVisible();

    //validate error message when 0
    await manageCourseDetailPage.reEnrollPassDayInputLocator.fill('0');
    await manageCourseDetailPage.saveButtonLocator.click();
    await expect(manageCourseDetailPage.reEnrollPassDayZeroErrorLocator).toBeVisible();
    await manageCourseDetailPage.discardButtonLocator.click();
    await manageCourseDetailPage.confirmDiscard();

    // @SKL-T20237 Admin แก้ไขเปิดใช้งานตั้งค่าเปิดลงทะเบียนซ้ำเมื่อผ่านหลักสูตรของหลักสูตรการเรียนทั่วไป และกรอกรูปแบบข้อมูลถูกต้อง, สามารถแก้ไขได้และบันทึกสำเร็จ
    //กดปุ่ม "แก้ไข" ของตั้งค่าเปิดลงทะเบียนซ้ำเมื่อผ่านหลักสูตร
    await manageCourseDetailPage.editReEnrollmentPassButtonLocator.click();

    //เปิดใช้งาน
    await manageCourseDetailPage.toggleReEnrollWhenPassLocator.click();

    //กรอกจำนวนวันที่ถูกต้อง
    await manageCourseDetailPage.fillReEnrollmentPassDayInput('30');

    //กดปุ่มบันทึก
    await manageCourseDetailPage.saveButtonLocator.click();

    //validate toast message "บันทึกข้อมูลสำเร็จ" และแสดงข้อมูลถูกต้อง
    await expect(manageCourseDetailPage.saveSuccessToastLocator.first()).toBeVisible();

    // @SKL-T20239 Admin แก้ไขปิดใช้งานตั้งค่าเปิดลงทะเบียนซ้ำเมื่อผ่านหลักสูตรของหลักสูตรการเรียนทั่วไป, สามารถแก้ไขได้และบันทึกสำเร็จ
    //กดปุ่ม "แก้ไข" ของตั้งค่าเปิดลงทะเบียนซ้ำเมื่อผ่านหลักสูตร
    await manageCourseDetailPage.editReEnrollmentPassButtonLocator.click();

    //ปิดใช้งาน
    await manageCourseDetailPage.toggleReEnrollWhenPassLocator.click();

    //กดปุ่มบันทึก
    await manageCourseDetailPage.saveButtonLocator.click();

    //validate toast message "บันทึกข้อมูลสำเร็จ" และแสดงข้อมูลถูกต้อง
    await expect(manageCourseDetailPage.saveSuccessToastLocator.first()).toBeVisible();

    // @SKL-T20235 Admin แก้ไขเปิดใช้งานตั้งค่าจำนวนวันที่เรียนได้ของหลักสูตรการเรียนทั่วไป และกรอกรูปแบบข้อมูลไม่ถูกต้อง, แสดง error และไม่สามารถบันทึกได้
    //กดปุ่ม "แก้ไข" ของตั้งค่าจำนวนวันที่เรียนได้
    await manageCourseDetailPage.editStudyDayButtonLocator.click();

    //เปิดใช้งาน
    await manageCourseDetailPage.toggleStudyDayLocator.click();

    //validate error message when empty "กรุณากรอกจำนวนวันที่เรียนได้"
    await manageCourseDetailPage.studyDayInputLocator.clear();
    await manageCourseDetailPage.saveButtonLocator.click();
    await expect(manageCourseDetailPage.studyDayEmptyErrorLocator).toBeVisible();

    //validate error message when 0 "กรุณากรอกจำนวนวันที่เรียนได้ โดยค่าต้องมากกว่า 0 เสมอ"
    await manageCourseDetailPage.studyDayInputLocator.fill('0');
    await manageCourseDetailPage.saveButtonLocator.click();
    await expect(manageCourseDetailPage.studyDayZeroErrorLocator).toBeVisible();
    await manageCourseDetailPage.discardButtonLocator.click();
    await manageCourseDetailPage.confirmDiscard();

    // @SKL-T20234 Admin แก้ไขเปิดใช้งานตั้งค่าจำนวนวันที่เรียนได้ของหลักสูตรการเรียนทั่วไป และกรอกรูปแบบข้อมูลถูกต้อง, สามารถแก้ไขได้และบันทึกสำเร็จ
    //กดปุ่ม "แก้ไข" ของตั้งค่าจำนวนวันที่เรียนได้
    await manageCourseDetailPage.editStudyDayButtonLocator.click();

    //เปิดใช้งาน
    await manageCourseDetailPage.toggleStudyDayLocator.click();

    //กรอกจำนวนวันที่ถูกต้อง
    await manageCourseDetailPage.fillStudyDayInput('30');

    //กดปุ่มบันทึก
    await manageCourseDetailPage.saveButtonLocator.click();

    //validate toast message "บันทึกข้อมูลสำเร็จ" และแสดงข้อมูลถูกต้อง
    await expect(manageCourseDetailPage.saveSuccessToastLocator.first()).toBeVisible();

    // @SKL-T20236 Admin แก้ไขปิดใช้งานตั้งค่าจำนวนวันที่เรียนได้ของหลักสูตรการเรียนทั่วไป, สามารถแก้ไขได้และบันทึกสำเร็จ

    //กดปุ่ม "แก้ไข" ของตั้งค่าจำนวนวันที่เรียนได้
    await manageCourseDetailPage.editStudyDayButtonLocator.click();

    //ปิดใช้งาน
    await manageCourseDetailPage.toggleStudyDayLocator.click();

    //กดปุ่มบันทึก
    await manageCourseDetailPage.saveButtonLocator.click();

    //validate toast message "บันทึกข้อมูลสำเร็จ" และแสดงข้อมูลถูกต้อง
    await expect(manageCourseDetailPage.saveSuccessToastLocator.first()).toBeVisible();

    // @SKL-T20232 Admin แก้ไขตั้งค่าปิดใช้งานการลงทะเบียนได้ด้วยตนเองของหลักสูตรการเรียนทั่วไป, สามารถแก้ไขได้และบันทึกสำเร็จ'
    //กดปุ่ม "แก้ไข" ของตั้งค่าการลงทะเบียน
    await manageCourseDetailPage.editSelfEnrollmentButtonLocator.click();

    //ปิดใช้งาน
    await manageCourseDetailPage.toggleSelfEnrollmentLocator.click();

    //กดปุ่มบันทึก
    await manageCourseDetailPage.saveButtonLocator.click();

    //validate toast message "บันทึกข้อมูลสำเร็จ" และแสดงข้อมูลถูกต้อง
    await expect(manageCourseDetailPage.saveSuccessToastLocator.first()).toBeVisible();

    // @SKL-T20233 Admin แก้ไขตั้งค่าเปิดใช้งานการลงทะเบียนได้ด้วยตนเองของหลักสูตรการเรียนทั่วไป, สามารถแก้ไขได้และบันทึกสำเร็จ
    //กดปุ่ม "แก้ไข" ของตั้งค่าการลงทะเบียน
    await manageCourseDetailPage.editSelfEnrollmentButtonLocator.click();

    //เปิดใช้งาน
    await manageCourseDetailPage.toggleSelfEnrollmentLocator.click();

    //กดปุ่มบันทึก
    await manageCourseDetailPage.saveButtonLocator.click();

    //validate toast message "บันทึกข้อมูลสำเร็จ" และแสดงข้อมูลถูกต้อง
    await expect(manageCourseDetailPage.saveSuccessToastLocator.first()).toBeVisible();

    // @SKL-T20215 Admin แก้ไขตั้งค่าจำนวนวันที่เรียนได้ของหลักสูตรการอบรม และกรอกรูปแบบข้อมูลไม่ถูกต้อง, แสดง error และไม่สามารถบันทึกได้
    //กดปุ่มแก้ไขตั้งค่าจำนวนวันที่เรียนได้
    await manageCourseDetailPage.editStudyDayButtonLocator.click();

    //เปิดใช้งาน
    await manageCourseDetailPage.toggleStudyDayLocator.click();

    //validate error message when empty "กรุณากรอกจำนวนวันที่เรียนได้"
    await manageCourseDetailPage.studyDayInputLocator.clear();
    await manageCourseDetailPage.saveButtonLocator.click();
    await expect(manageCourseDetailPage.studyDayEmptyErrorLocator).toBeVisible();

    //validate error message when 0 "กรุณากรอกจำนวนวันที่เรียนได้ โดยค่าต้องมากกว่า 0 เสมอ"
    await manageCourseDetailPage.studyDayInputLocator.fill('0');
    await manageCourseDetailPage.saveButtonLocator.click();
    await expect(manageCourseDetailPage.studyDayZeroErrorLocator).toBeVisible();
    await manageCourseDetailPage.discardButtonLocator.click();
    await manageCourseDetailPage.confirmDiscard();

    // @SKL-T20214 Admin แก้ไขตั้งค่าจำนวนวันที่เรียนได้ของหลักสูตรการอบรม และกรอกรูปแบบข้อมูลถูกต้อง, สามารถแก้ไขได้และบันทึกสำเร็จ
    //กดปุ่มแก้ไขตั้งค่าจำนวนวันที่เรียนได้
    await manageCourseDetailPage.editStudyDayButtonLocator.click();

    //เปิดใช้งาน
    await manageCourseDetailPage.toggleStudyDayLocator.click();

    //กรอกจำนวนวันที่ถูกต้อง
    await manageCourseDetailPage.fillStudyDayInput('30');

    //กดปุ่มบันทึก
    await manageCourseDetailPage.saveButtonLocator.click();

    //validate toast message "บันทึกข้อมูลสำเร็จ" และแสดงข้อมูลถูกต้อง
    await expect(manageCourseDetailPage.saveSuccessToastLocator.first()).toBeVisible();
  });

  // *ACCESSING MENU
  test('@SKL-T20230 Admin แก้ไขการเข้าถึงของหลักสูตรการเรียนทั่วไป เป็น เนื้อหาการเรียนแบบเจาะจงกลุ่มผู้ใช้งาน กรณีไม่มีกลุ่มผู้ใช้งาน, แสดง error และไม่สามารถบันทึกได้', async ({
    //NOTE: แก้ไขหลังจากข้อนี้หรือควรสร้างคอร์สไว้ แล้วไปแก้ไขโดยเช็คว่าถ้าเลือก1 ให้เลือก2 ถ้าเลือก2 ให้เลือก1
    page,
    configuration,
    loginPage,
    manageCourseListPage,
    manageCourseDetailPage,
    adminDashboardPage,
    homePage,
  }) => {
    const adminTLI = configuration.shareUsers.adminRequestDocument;
    const course = configuration.shareCourses.regularManageGeneral;
    const userGroupName = 'Automate_UserGroup';

    // Admin login
    await loginPage.loginWithUsernameAndPassword(adminTLI.username, adminTLI.password);
    await expect(homePage.userProfileLocator).toBeVisible();
    await adminDashboardPage.accessCoursesMenu();
    await manageCourseListPage.searchCourseName(course.courseVersions[1].name);
    await manageCourseListPage.viewCourseDetail();

    // Access accessing menu
    await manageCourseDetailPage.accessingMenuLocator.click();
    await expect(manageCourseDetailPage.accessingMenuLocator).toBeVisible();

    await manageCourseDetailPage.editAccessButtonLocator.click();

    //เลือกเนื้อหาการเรียนแบบส่วนบุคคล
    await manageCourseDetailPage.selectPrivateAccessLocator.click();

    //กดปุ่มบันทึก
    await manageCourseDetailPage.saveButtonLocator.click();

    //validate toast message "บันทึกข้อมูลสำเร็จ" และแสดงข้อมูลถูกต้อง
    await expect(manageCourseDetailPage.saveSuccessToastLocator.first()).toBeVisible();

    //กดปุ่ม "แก้ไข" ของการเข้าถึง
    await manageCourseDetailPage.editAccessButtonLocator.click();

    //เลือกเนื้อหาการเรียนแบบเจาะจงกลุ่มผู้ใช้งาน
    await manageCourseDetailPage.selectSpecificUserGroupAccessLocator.click();

    //กดปุ่มบันทึก
    await manageCourseDetailPage.saveButtonLocator.click();

    //validate error message "กรุณาเลือกกลุ่มผู้ใช้งานอย่างน้อย 1 กลุ่ม"
    await expect(manageCourseDetailPage.noUserGroupErrorLocator).toContainText(
      'กรุณาเลือกกลุ่มผู้ใช้งานอย่างน้อย 1 กลุ่ม',
    );
    await manageCourseDetailPage.drawerCloseButtonLocator.click();

    // @SKL-T20227 Admin แก้ไขการเข้าถึงของหลักสูตรการเรียนทั่วไป เป็นเนื้อหาการเรียนแบบสาธารณะ, สามารถแก้ไขได้และบันทึกสำเร็จ
    //กดปุ่ม "แก้ไข" ของการเข้าถึง
    await manageCourseDetailPage.editAccessButtonLocator.click();

    //เลือกเนื้อหาการเรียนแบบสาธารณะ
    await manageCourseDetailPage.selectPublicAccessLocator.click();

    //กดปุ่มบันทึก
    await manageCourseDetailPage.saveButtonLocator.click();

    //validate toast message "บันทึกข้อมูลสำเร็จ" และแสดงข้อมูลถูกต้อง
    await expect(manageCourseDetailPage.saveSuccessToastLocator.first()).toBeVisible();

    // @SKL-T20228 Admin แก้ไขการเข้าถึงของหลักสูตรการเรียนทั่วไป เป็น เนื้อหาการเรียนแบบส่วนบุคคล, สามารถแก้ไขได้และบันทึกสำเร็จ
    //กดปุ่ม "แก้ไข" ของการเข้าถึง
    await manageCourseDetailPage.editAccessButtonLocator.click();

    //เลือกเนื้อหาการเรียนแบบส่วนบุคคล
    await manageCourseDetailPage.selectPrivateAccessLocator.click();

    //กดปุ่มบันทึก
    await manageCourseDetailPage.saveButtonLocator.click();

    //validate toast message "บันทึกข้อมูลสำเร็จ" และแสดงข้อมูลถูกต้อง
    await expect(manageCourseDetailPage.saveSuccessToastLocator.first()).toBeVisible();

    //@SKL-T20229 Admin แก้ไขการเข้าถึงของหลักสูตรการเรียนทั่วไป เป็น เนื้อหาการเรียนแบบเจาะจงกลุ่มผู้ใช้งาน กรณีเพิ่มกลุ่มผู้ใช้งาน, สามารถแก้ไขได้และบันทึกสำเร็จ
    //กดปุ่ม "แก้ไข" ของการเข้าถึง
    //
    await manageCourseDetailPage.editAccessButtonLocator.click();

    //เลือกเนื้อหาการเรียนแบบเจาะจงกลุ่มผู้ใช้งาน
    await manageCourseDetailPage.selectSpecificUserGroupAccessLocator.click();

    //เลือกกลุ่มผู้ใช้งาน
    await manageCourseDetailPage.userGroupSearchInputLocator.click();
    await manageCourseDetailPage.userGroupSearchInputLocator.fill(userGroupName);
    await manageCourseDetailPage.userGroupOptionLocator.filter({ hasText: userGroupName }).click();
    await manageCourseDetailPage.addUserGroupButtonLocator.click();

    // validate กลุ่มที่เลือกแสดงในตารางอย่างถูกต้อง
    await manageCourseDetailPage.validateSelectedUserGroup(userGroupName);

    await manageCourseDetailPage.saveButtonLocator.click();

    //validate toast message
    await expect(manageCourseDetailPage.saveSuccessToastLocator.first()).toBeVisible();

    //@SKL-T20231 Admin แก้ไขการเข้าถึงของหลักสูตรการเรียนทั่วไป เป็น เนื้อหาการเรียนแบบเจาะจงกลุ่มผู้ใช้งาน กรณีนำกลุ่มผู้ใช้งานออก, สามารถแก้ไขได้และบันทึกสำเร็จ
    await manageCourseDetailPage.editAccessButtonLocator.click();

    await page.pause();
    //เลือกเนื้อหาการเรียนแบบเจาะจงกลุ่มผู้ใช้งาน
    await manageCourseDetailPage.selectSpecificUserGroupAccessLocator.click();

    // add 1 more Automate For User Group
    await manageCourseDetailPage.userGroupSearchInputLocator.click();
    await manageCourseDetailPage.userGroupSearchInputLocator.fill('Automate For User Group');
    await manageCourseDetailPage.userGroupOptionLocator.filter({ hasText: 'Automate For User Group' }).click();
    await manageCourseDetailPage.addUserGroupButtonLocator.click();

    //กลุ่มผู้ใช้งานนำออก
    await manageCourseDetailPage.removeUserGroupLocator.click();

    //กดปุ่มบันทึก
    await manageCourseDetailPage.saveButtonLocator.click();

    //validate toast message "บันทึกข้อมูลสำเร็จ" และแสดงข้อมูลถูกต้อง
    await expect(manageCourseDetailPage.saveSuccessToastLocator.first()).toBeVisible();
  });

  // *INSTRUCTOR MENU
  test('@SKL-T20202 Admin แก้ไขผู้สอน เพิ่มผู้สอนของหลักสูตรการอบรม กรณีมีผู้สอนแล้วบนระบบ, สามารถแก้ไขได้และบันทึกสำเร็จ', async ({
    configuration,
    loginPage,
    homePage,
    adminDashboardPage,
    manageCourseListPage,
    manageCourseDetailPage,
  }) => {
    const organizationAdmin = configuration.usersLocal.organizationAdminTLI1;
    const course = configuration.shareCourses.regularManageGeneral;
    const intructorName = 'AutomateTeacher';

    // Admin login
    await loginPage.loginWithUsernameAndPassword(organizationAdmin.username, organizationAdmin.password);
    await expect(homePage.userProfileLocator).toBeVisible();
    await adminDashboardPage.accessCoursesMenu();
    await manageCourseListPage.searchCourseName(course.courseVersions[1].name);
    await manageCourseListPage.viewCourseDetail();

    // Access instructor menu
    await manageCourseDetailPage.instructorMenuLocator.click();
    await expect(manageCourseDetailPage.instructorMenuLocator).toBeVisible();

    // Click edit instructor button
    await manageCourseDetailPage.editInstructorButtonLocator.click();

    // Add instructor
    await manageCourseDetailPage.inputInstructorsLocator.fill(intructorName);
    await manageCourseDetailPage.selectItemInstructorsLocator.click();
    await manageCourseDetailPage.addInstructorButtonLocator.click();

    // Save and validate toast message success
    await manageCourseDetailPage.saveButtonLocator.click();
    await expect(manageCourseDetailPage.addInstructorSuccessToastLocator.first()).toBeVisible();

    // @SKL-T20203 Admin แก้ไขผู้สอน สร้างผู้สอนของหลักสูตรการอบรม กรณีไม่มีผู้สอนบนระบบ, สามารถแก้ไขได้และบันทึกสำเร็จ
    const newInstructorData = {
      firstName: 'NewInstructorName',
      lastName: 'Test',
      email: '<EMAIL>',
      phone: '0812345678',
      highlightDescription: 'Test instructor description',
      biology: 'Test instructor biography',
    };
    await manageCourseDetailPage.editInstructorButtonLocator.click();

    //create new instructor
    await manageCourseDetailPage.createNewInstructor(newInstructorData);

    //add new instructor
    await manageCourseDetailPage.addInstructor(newInstructorData.firstName);

    //save and validate toast message success
    await manageCourseDetailPage.saveButtonLocator.click();
    await expect(manageCourseDetailPage.addInstructorSuccessToastLocator.first()).toBeVisible();

    //validate data
    await manageCourseDetailPage.validateInstructorInList(newInstructorData.firstName);

    // @SKL-T20206 Admin แก้ไขผู้สอน เรียงผู้สอนของหลักสูตรการอบรม, สามารถแก้ไขได้และบันทึกสำเร็จ
    await manageCourseDetailPage.editInstructorButtonLocator.click();

    //เรียงลำดับ - capture initial order and perform reordering
    const initialOrder = await manageCourseDetailPage.instructorNameListLocator.allTextContents();
    const trimmedInitialOrder = initialOrder.map((name) => name.trim());
    console.log('Initial instructor order:', trimmedInitialOrder);

    // Perform instructor reordering using drag and drop
    await manageCourseDetailPage.reorderInstructors();

    // Expected order after moving first instructor to second position
    const expectedOrder =
      trimmedInitialOrder.length >= 2
        ? [trimmedInitialOrder[1], trimmedInitialOrder[0], ...trimmedInitialOrder.slice(2)]
        : trimmedInitialOrder;

    //save and validate toast message success
    await manageCourseDetailPage.saveButtonLocator.click();
    await expect(manageCourseDetailPage.addInstructorSuccessToastLocator.first()).toBeVisible();

    //validate order of instructor
    await manageCourseDetailPage.validateInstructorOrder(expectedOrder);

    // @SKL-T20204 Admin แก้ไขผู้สอน ลบผู้สอนของหลักสูตรการอบรม, สามารถแก้ไขได้และบันทึกสำเร็จ
    await manageCourseDetailPage.editInstructorButtonLocator.click();

    const initialInstructors = await manageCourseDetailPage.instructorListInDndTableLocator.allTextContents();
    const instructorToDelete = initialInstructors[0]?.trim();

    await manageCourseDetailPage.deleteInstructor(0);

    //save and validate toast message success
    await manageCourseDetailPage.saveButtonLocator.click();
    await expect(manageCourseDetailPage.addInstructorSuccessToastLocator.first()).toBeVisible();
    await manageCourseDetailPage.addInstructorSuccessToastLocator.first().waitFor({ state: 'hidden' });

    //validate data - check that the instructor was removed
    if (instructorToDelete) {
      const currentInstructors = await manageCourseDetailPage.instructorNameListLocator.allTextContents();
      const trimmedCurrentInstructors = currentInstructors.map((name) => name.trim());
      expect(trimmedCurrentInstructors).not.toContain(instructorToDelete);
    }

    // delete another instructor left
    await manageCourseDetailPage.editInstructorButtonLocator.click();
    await manageCourseDetailPage.deleteInstructor(0);
    await manageCourseDetailPage.saveButtonLocator.click();
    await expect(manageCourseDetailPage.addInstructorSuccessToastLocator.first()).toBeVisible();
  });

  test('@SKL-T20200 Admin แก้ไขข้อมูลคุณสมบัติหลักสูตรของหลักสูตรการอบรมวิชาชีพประกัน (OIC) กรณีกรอกไม่ข้อมูลถูกต้อง, แสดง error และไม่สามารถบันทึกได้', async ({
    //NOTE: แก้ไขหลังจากข้อนี้หรือควรสร้างคอร์สไว้ แล้วไปแก้ไขโดยเช็คว่าถ้าเลือก1 ให้เลือก2 ถ้าเลือก2 ให้เลือก1
    page,
    configuration,
    loginPage,
    manageCourseListPage,
    organizationAdminDashboardPage,
    manageCourseDetailPage,
  }) => {
    const adminTLI = configuration.shareUsers.adminRequestDocument;
    const course = configuration.shareCourses.regularManageGeneral;
    // Admin login
    await loginPage.loginWithUsernameAndPassword(adminTLI.username, adminTLI.password);
    await organizationAdminDashboardPage.accessManageCourseList();

    // ---
    const courseName = '[Automate][TEMP] Admin create OIC course';
    const courseCode = 'AUTOMATE_OIC_TEMP';
    // Admin login
    await expect(manageCourseListPage.courseRowsLocator.first()).toBeVisible();
    await expect(manageCourseListPage.searchInputLocator).toBeVisible();
    await expect(manageCourseListPage.searchSubmitBtnLocator).toBeVisible();
    await expect(manageCourseListPage.createCourseButtonLocator).toBeVisible();
    await manageCourseListPage.createCourse();
    await manageCourseListPage.createCourseForm.selectObjectiveCourse(ObjectiveCourse.OIC);
    await manageCourseListPage.createCourseForm.fillCourseCode(courseCode);
    await manageCourseListPage.createCourseForm.fillCourseName(courseName);
    await manageCourseListPage.createCourseForm.submitCreateCourseButton();
    await expect(manageCourseListPage.toastMsg.validateMsgLocator).toContainText('สร้างหลักสูตรสำเร็จ');

    // Access edit course properties
    await manageCourseDetailPage.editCoursePropertiesButtonLocator.click();

    //กดบันทึกข้อมูล
    await manageCourseDetailPage.saveButtonLocator.click();

    //validate error message when empty
    await page.pause();
    await expect(manageCourseDetailPage.trainingCenterErrorLocator).toContainText('กรุณาเลือกศูนย์อบรม');
    await expect(manageCourseDetailPage.licenseTypeErrorLocator).toContainText('กรุณาเลือกประเภทใบอนุญาต');
    await expect(manageCourseDetailPage.applicantTypeErrorLocator).toContainText('กรุณาเลือกประเภทผู้สมัคร');
    await expect(manageCourseDetailPage.insuranceTypeErrorLocator).toContainText('กรุณาเลือกประเภทการประกัน');

    // @SKL-T20198 Admin แก้ไขข้อมูลคุณสมบัติหลักสูตรของหลักสูตรการอบรมวิชาชีพประกัน (OIC) กรณีกรอกข้อมูลถูกต้อง, สามารถแก้ไขได้และบันทึกสำเร็จ

    // Fill course properties form with correct data
    await manageCourseDetailPage.fillCoursePropertiesForm();

    // Save the form
    await manageCourseDetailPage.saveButtonLocator.click();

    // Validate success toast message
    await expect(manageCourseDetailPage.saveSuccessToastLocator).toBeVisible();
  });
  test('', async ({ configuration, loginPage, manageCourseListPage, adminDashboardPage, manageCourseDetailPage }) => {
    const adminTLI = configuration.shareUsers.adminRequestDocument;
    const course = configuration.shareCourses.regularManageGeneral;

    // Admin login
    await loginPage.loginWithUsernameAndPassword(adminTLI.username, adminTLI.password);
    await adminDashboardPage.accessCoursesMenu();
    await manageCourseListPage.searchCourseName(course.courseVersions[1].name);
    await manageCourseListPage.viewCourseDetail();

    // Access edit course properties
    await manageCourseDetailPage.editCoursePropertiesButtonLocator.click();
  });
  test('@SKL-T20197 Admin แก้ไขข้อมูลภาพรวมของหลักสูตรการอบรม กรณีกรอกข้อมูลไม่ถูกต้อง, แสดง error และไม่สามารถบันทึกได้', async ({
    configuration,
    loginPage,
    homePage,
    adminDashboardPage,
    manageCourseListPage,
    manageCourseDetailPage,
  }) => {
    const organizationAdmin = configuration.usersLocal.organizationAdminTLI1;
    const oicCourse = configuration.shareCourses.courseOICInstructor; // Using available OIC course

    // Admin login
    await loginPage.loginWithUsernameAndPassword(organizationAdmin.username, organizationAdmin.password);
    await expect(homePage.userProfileLocator).toBeVisible();
    await adminDashboardPage.accessCoursesMenu();
    await manageCourseListPage.searchCourseName(oicCourse.name);
    await manageCourseListPage.viewCourseDetail();

    // Edit training course overview with incorrect data
    await manageCourseDetailPage.accessCourseGeneralDetail(oicCourse.name);
    await manageCourseDetailPage.editCourseName(''); // Empty name should cause error
    await manageCourseDetailPage.submitSaveButton();
    // TODO: Add validation error verification when method is available
    // For now, we expect the save to fail silently or show a toast error
  });
  test('@SKL-T20195 Admin แก้ไขข้อมูลภาพรวมของหลักสูตรการอบรม กรณีกรอกข้อมูลถูกต้อง, สามารถแก้ไขได้และบันทึกสำเร็จ', async ({
    configuration,
    loginPage,
    homePage,
    adminDashboardPage,
    manageCourseListPage,
    manageCourseDetailPage,
  }) => {
    const organizationAdmin = configuration.usersLocal.organizationAdminTLI1;
    const oicCourse = configuration.shareCourses.courseOICInstructor; // Using available OIC course

    // Admin login
    await loginPage.loginWithUsernameAndPassword(organizationAdmin.username, organizationAdmin.password);
    await expect(homePage.userProfileLocator).toBeVisible();
    await adminDashboardPage.accessCoursesMenu();
    await manageCourseListPage.searchCourseName(oicCourse.name);
    await manageCourseListPage.viewCourseDetail();

    // Edit training course overview with correct data
    await manageCourseDetailPage.accessCourseGeneralDetail(oicCourse.name);
    await manageCourseDetailPage.editCourseName('Updated Training Course - Valid');
    await manageCourseDetailPage.submitSaveButton();
    await manageCourseDetailPage.toastMessageSaveSuccess();
  });
});
