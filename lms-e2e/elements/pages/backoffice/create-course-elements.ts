import { Locator, Page, expect } from '@playwright/test';
import { BaseElement } from '../../components/base-element';

export enum ObjectiveCourse {
  Regular = 'เรียนเนื้อหาทั่วไป',
  OIC = 'อบรมวิชาชีพประกัน (คปภ.)',
  UL = 'ประกอบวิชาชีพการประกัน',
  TSI = 'อบรมวิชาชัำการลงทุน (TSI)',
}

export class CreateCourseElement extends BaseElement {
  readonly selectObjectiveCourseRegularLocator: Locator = this.page.locator('#objectiveType');
  readonly selectTrainingObjectiveTypeLocator: Locator = this.page.locator('#regulator');
  // readonly objectiveRegularOptionLocator: Locator = this.page.getByTitle('เรียนเนื้อหาทั่วไป');
  readonly objectiveTypeOicOptionLocator: Locator = this.page.locator('.ant-select-item-option-content', {
    hasText: 'อบรมวิชาชีพประกัน (OIC)',
  });
  readonly tsiTrainingObjectiveTypeOptionLocator: Locator = this.page.locator('.ant-select-item-option-content', {
    hasText: 'อบรมวิชาชีพการลงทุน (TSI)',
  });
  readonly courseCodeInputLocator: Locator = this.elementLocator.getByPlaceholder('กรอกรหัสเนื้อหา');
  readonly courseNameInputLocator: Locator = this.elementLocator.getByPlaceholder('กรอกชื่อเนื้อหา');
  readonly submitCreateCourseButtonLocator: Locator = this.elementLocator.getByRole('button', {
    name: 'สร้าง',
    exact: true,
  });
  readonly trainingCourseObjectiveTypeOptionLocator: Locator = this.page.locator('.ant-select-item-option-content', {
    hasText: 'การอบรม',
  });
  readonly objectiveRegularOptionLocator: Locator = this.page.locator('.ant-select-item-option-content', {
    hasText: 'การเรียนทั่วไป',
  });

  // Error message locators
  readonly objectiveErrorMessageLocator: Locator = this.elementLocator.locator(
    '[data-testid="objective-type-form-item"] .ant-form-item-explain-error',
  );
  readonly courseCodeErrorMessageLocator: Locator = this.elementLocator.locator(
    '[data-testid="code-form-item"] .ant-form-item-explain-error',
  );
  readonly courseNameErrorMessageLocator: Locator = this.elementLocator.locator(
    '[data-testid="name-form-item"] .ant-form-item-explain-error',
  );
  readonly regulatorErrorMessageLocator: Locator = this.elementLocator.locator(
    '[data-testid="regulator-form-item"] .ant-form-item-explain-error',
  );

  constructor(page: Page, elementLocator: Locator) {
    super(page, elementLocator);
  }

  async selectObjectiveCourse(value: string | ObjectiveCourse): Promise<this> {
    await this.selectObjectiveCourseRegularLocator.click();
    await this.page.pause();
    switch (value) {
      case ObjectiveCourse.Regular: {
        await this.objectiveRegularOptionLocator.click();
        break;
      }
      case ObjectiveCourse.OIC: {
        await this.trainingCourseObjectiveTypeOptionLocator.click();
        await this.selectTrainingObjectiveTypeLocator.click();
        await this.objectiveTypeOicOptionLocator.click();
        break;
      }
      case ObjectiveCourse.TSI: {
        await this.trainingCourseObjectiveTypeOptionLocator.click();
        await this.selectTrainingObjectiveTypeLocator.click();
        await this.tsiTrainingObjectiveTypeOptionLocator.click();
        break;
      }
      default: {
        break;
      }
    }
    return this;
  }

  async fillCourseCode(value: string): Promise<this> {
    await this.courseCodeInputLocator.fill(value);

    return this;
  }

  async fillCourseName(value: string): Promise<this> {
    await this.courseNameInputLocator.fill(value);

    return this;
  }

  async submitCreateCourseButton(): Promise<this> {
    await this.submitCreateCourseButtonLocator.click();

    return this;
  }

  // Error validation helper methods
  async validateObjectiveErrorMessage(expectedMessage: string): Promise<this> {
    await expect(this.objectiveErrorMessageLocator.filter({ hasText: expectedMessage })).toBeVisible();
    return this;
  }

  async validateCourseCodeErrorMessage(expectedMessage: string): Promise<this> {
    await expect(this.courseCodeErrorMessageLocator.filter({ hasText: expectedMessage })).toBeVisible();
    return this;
  }

  async validateCourseNameErrorMessage(expectedMessage: string): Promise<this> {
    await expect(this.courseNameErrorMessageLocator.filter({ hasText: expectedMessage })).toBeVisible();
    return this;
  }

  async validateRegulatorErrorMessage(expectedMessage: string): Promise<this> {
    await expect(this.regulatorErrorMessageLocator.filter({ hasText: expectedMessage })).toBeVisible();
    return this;
  }

  async clearForm(): Promise<this> {
    await this.courseCodeInputLocator.clear();
    await this.courseNameInputLocator.clear();
    return this;
  }
}
