import { Locator, Page, expect } from '@playwright/test';
import { BasePage } from '../base-page';
import { GeneralCourseDetailElmentsPage } from './general-course-detail-elements';
import { CertificateCourseDetailElementsPage } from './certificate-course-detail-elements';

export class ManageCourseDetailPage extends BasePage {
  readonly tabCurriculumButtonLocator: Locator = this.page.getByRole('tab', { name: 'บทเรียน' });
  readonly tabGeneralInfoButtonLocator: Locator = this.page.getByRole('tab', { name: 'ข้อมูลทั่วไป' });
  readonly editCourseFeaturesButtonLocator: Locator = this.page.getByRole('button', { name: 'แก้ไข' }).nth(1);
  readonly licenseRenewalLocator: Locator = this.page.getByLabel('สถานะใบอนุญาต');
  readonly licenseTsiLocator: Locator = this.page.getByText('ขอรับใบอนุญาต');
  readonly licenseOic1SelectDropDownLocator: Locator = this.page.getByText('ต่ออายุครั้งที่ 1');
  readonly licenseOic2SelectDropDownLocator: Locator = this.page.getByText('ต่ออายุครั้งที่ 2');
  readonly licenseOic3SelectDropDownLocator: Locator = this.page.getByText('ต่ออายุครั้งที่ 3');

  readonly licenseOic4SelectDropDownLocator: Locator = this.page.getByText('ต่ออายุครั้งที่ 4 เป็นต้นไป');
  readonly oic4NonDeductLocator: Locator = this.page.getByText('สำหรับผู้ไม่มีสิทธิ์ลดหย่อนชั่วโมงการอบรม');
  readonly oic4DeductWithDocumentLocator: Locator = this.page.getByText(
    'สำหรับผู้มีสิทธิ์ลดหย่อนที่ไม่ต้องแนบหลักฐานการลดหย่อน',
  );
  readonly oic4DeductWithoutDocumentLocator: Locator = this.page.getByText(
    'สำหรับผู้มีสิทธิ์ลดหย่อนที่จำเป็นต้องแนบหลักฐานการลดหย่อนในระบบ',
  );

  readonly applicantTypeLocator: Locator = this.page.getByLabel('ประเภทผู้สมัคร');
  readonly agentSelectDropDownLocator: Locator = this.page.getByText('ตัวแทน');
  readonly licenseTypeLocator: Locator = this.page.getByLabel('ประเภทการประกัน');
  readonly lifeLicenseDropDownLocator: Locator = this.page.getByText('ประกันชีวิต', { exact: true });
  readonly saveButtonLocator: Locator = this.page.getByRole('button', { name: 'บันทึก', exact: true });
  readonly discardButtonLocator: Locator = this.page.getByRole('button', { name: 'ละทิ้ง' });
  readonly confirmDiscardButtonLocator: Locator = this.page
    .locator('.ant-modal-content')
    .getByRole('button', { name: 'ละทิ้ง' });
  readonly certificateMenuLocator: Locator = this.page.getByRole('menuitem', { name: 'ประกาศนียบัตร' });
  readonly learningMenuLocator: Locator = this.page.getByRole('menuitem', { name: 'การเรียน', exact: true });
  readonly editCertificateButtonLocator: Locator = this.page.getByRole('button', { name: 'แก้ไข' }).nth(1);
  readonly editCriteriaButtonLocator: Locator = this.page.getByRole('button', { name: 'แก้ไข' }).nth(2);
  readonly completeButtonLocator: Locator = this.page.getByRole('button', { name: 'เสร็จสิ้น' });

  readonly enrollMenuLocator: Locator = this.page.getByRole('menuitem', { name: 'การลงทะเบียน' });
  readonly editEnrollButtonLocator: Locator = this.page.getByRole('button', { name: 'แก้ไข' }).nth(2);
  readonly verifyHeadingEnroll: Locator = this.page.getByRole('heading', {
    name: 'ตั้งค่าเปิดลงทะเบียนซ้ำเมื่อผ่านหลักสูตร',
    exact: true,
  });
  readonly enableReEnrollButtonLocator: Locator = this.page.locator('#isReEnrollEnabled');
  readonly verifyTextReEnroll: Locator = this.page.getByText('เปิดให้ลงทะเบียนซ้ำเมื่อผ่านหลักสูตร').first();

  readonly publishContentButtonLocator: Locator = this.page.getByRole('button', { name: 'เผยแพร่เนื้อหา' });

  readonly browseFileLocator: Locator = this.page.locator('data-testid=browse-file');
  readonly createVideoChapterButtonLocator: Locator = this.page.locator('data-testid=create-video-chapter-button');

  readonly courseNameInputLocator: Locator = this.page.locator('id=courseName');
  readonly courseNameErrorLocator: Locator = this.page.getByText('กรุณากรอกชื่อเนื้อหา');
  readonly courseDescriptionEditorLocator: Locator = this.page.locator('.tiptap.ProseMirror[contenteditable="true"]');
  readonly courseNameLeadingSpaceErrorLocator: Locator = this.page.getByText(
    'ชื่อเนื้อหาไม่สามารถเริ่มต้นด้วยเว้นวรรค',
  );
  readonly courseNameTrailingSpaceErrorLocator: Locator = this.page.getByText('ชื่อเนื้อหาไม่สามารถลงท้ายด้วยเว้นวรรค');
  readonly courseNameConsecutiveSpaceErrorLocator: Locator = this.page.getByText(
    'ชื่อเนื้อหาไม่สามารถมีเว้นวรรคติดกันได้',
  );
  readonly courseNameTooLongErrorLocator: Locator = this.page.getByText('ชื่อเนื้อหาควรมีความยาวไม่เกิน 300 ตัวอักษร');
  readonly thumbnailUploadInputLocator: Locator = this.page.locator('input#image[type="file"]');
  readonly thumbnailUploadErrorLocator: Locator = this.page.getByText(
    'กรุณาเลือกอัปโหลดรูปแบบไฟล์ .png, .jpg, .jpeg และขนาดไม่เกิน 30 MB',
  );
  readonly searchButtonLocator: Locator = this.page.locator(
    'span.ant-input-group-addon >> button.ant-input-search-button >> span.anticon-search',
  );
  readonly courseDetailInstructorsMenu: Locator = this.page
    .getByLabel('ข้อมูลทั่วไป')
    .getByRole('menu')
    .getByText('ผู้สอน');
  readonly editButtonLocator: Locator = this.page.getByRole('button', { name: 'แก้ไข' });
  readonly inputInstructorsLocator: Locator = this.page.locator('id=instructorId');
  readonly selectInstructorsLocator: Locator = this.page.getByText('Test Automate - <EMAIL>').first();
  readonly selectItemInstructorsLocator: Locator = this.page.locator('div.ant-select-item-option-content').first();
  readonly addInstructorButtonLocator: Locator = this.page.getByRole('button', { name: 'เพิ่มผู้สอน' });
  readonly loadingSkeleton: Locator = this.page
    .locator('td > .ant-skeleton > .ant-skeleton-content > .ant-skeleton-title')
    .first();
  readonly loadingAnimation: Locator = this.page.locator('text=กำลังโหลดข้อมูล');
  readonly loadingSpinLocator: Locator = this.page.locator('css=div.ant-spin-spinning');
  readonly editInstructorButtonLocator: Locator = this.page.getByRole('button', { name: 'edit แก้ไข' });
  readonly backButtonLocator: Locator = this.page.getByRole('button').first();
  readonly publishedNewVersionButtonLocator: Locator = this.page.getByRole('button', { name: 'เผยแพร่เวอร์ชันใหม่' });
  readonly deleteDraftVersionButtonLocator: Locator = this.page.getByRole('button', { name: 'ยกเลิกร่าง' });
  readonly selectObjectivetypeSycnFromB2CLocator: Locator = this.page.getByLabel('วัตถุประสงค์');
  readonly detailCourseNameInfoLocator: Locator = this.page.locator('div:right-of(:text("ชื่อหลักสูตร"))');
  readonly inputCourseNameLocator: Locator = this.page.getByPlaceholder('กรอกชื่อเนื้อหา');
  readonly hideContentButtonLocator: Locator = this.page
    .getByRole('dialog')
    .getByRole('button', { name: 'ซ่อนทั้งหมด' });
  readonly selectContentInCurriculumLocator: Locator = this.page.locator(
    'div.ant-collapse-header.ant-collapse-header-collapsible-only',
  );
  readonly toggleInnerContentButtonLocator: Locator = this.page.locator('span.ant-switch-inner');
  readonly deleteContentButtonLocator: Locator = this.page.locator('span.anticon-delete');
  readonly confirmDeleteButtonLocator: Locator = this.page.locator(
    'div.ant-modal-footer button.ant-btn-primary:has-text("ลบ")',
  );
  readonly confirmButtonLocator: Locator = this.page.getByRole('button', { name: 'ยืนยัน' });

  readonly instructorMenuLocator: Locator = this.page
    .locator('.detail-card-container')
    .getByRole('menuitem')
    .filter({ hasText: 'ผู้สอน' });
  readonly manageInstructorMenuLocator: Locator = this.page.getByRole('link', { name: 'ผู้สอน' });
  readonly accessingMenuLocator: Locator = this.page.getByRole('menuitem', { name: 'การเข้าถึง' });
  readonly headingSettingAccessingLocator: Locator = this.page
    .getByLabel('ข้อมูลทั่วไป')
    .getByRole('menu')
    .getByText('การเข้าถึง');
  readonly headingSettingInstructorLocator: Locator = this.page.getByText('ผู้สอน (0)');
  readonly headingSettingEnrollmentLocator: Locator = this.page.getByText('ตั้งค่าการลงทะเบียน');
  readonly headingSettingTotalDateTrainingLocator: Locator = this.page.getByText('ตั้งค่าจำนวนวันที่เรียนได้');
  readonly headingSettingReEnrollmentPassLocator: Locator = this.page.getByText(
    'ตั้งค่าเปิดลงทะเบียนซ้ำเมื่อผ่านหลักสูตร',
  );
  readonly headingSettingReEnrollmentExpireLocator: Locator = this.page.getByText(
    'ตั้งค่าเปิดลงทะเบียนซ้ำเมื่อหลักสูตรหมดอายุ',
  );
  readonly editReEnrollmentExpireButtonLocator: Locator = this.page
    .locator('.detail-card-container:has-text("ตั้งค่าเปิดลงทะเบียนซ้ำเมื่อหลักสูตรหมดอายุ")')
    .getByRole('button', { name: 'แก้ไข' });
  readonly editReEnrollmentPassButtonLocator: Locator = this.page
    .locator('.detail-card-container:has-text("ตั้งค่าเปิดลงทะเบียนซ้ำเมื่อผ่านหลักสูตร")')
    .getByRole('button', { name: 'แก้ไข' });
  readonly editSelfEnrollmentButtonLocator: Locator = this.page
    .locator('.detail-card-container:has-text("ตั้งค่าการลงทะเบียน")')
    .getByRole('button', { name: 'แก้ไข' });
  readonly editAccessButtonLocator: Locator = this.page
    .locator('.detail-card-container:has-text("การเข้าถึง")')
    .getByRole('button', { name: 'แก้ไข' });
  readonly editStudyDayButtonLocator: Locator = this.page
    .locator('.detail-card-container:has-text("ตั้งค่าจำนวนวันที่เรียนได้")')
    .getByRole('button', { name: 'แก้ไข' });
  readonly headingSettingCourseStartLocator: Locator = this.page.getByText('ตั้งค่าการเริ่มหลักสูตร');
  readonly editCourseStartButtonLocator: Locator = this.page
    .locator('.detail-card-container:has-text("ตั้งค่าการเริ่มหลักสูตร")')
    .getByRole('button', { name: 'แก้ไข' });
  readonly headingSettingGeneralLocator: Locator = this.page.getByText('ตั้งค่าการเรียนเนื้อหาทั่วไป');
  readonly headingSettingInterestLocator: Locator = this.page.getByText('ตั้งค่าความสนใจระหว่างเรียน');
  readonly headingSettingCertificateLocator: Locator = this.page.getByText('ตั้งค่าประกาศนียบัตร');
  readonly headingConditionCertificateLocator: Locator = this.page.getByText('เงื่อนไขการได้รับประกาศนียบัตร');
  readonly headingConfirmCancelLocator: Locator = this.page.locator('div.ant-modal-header >> div.ant-modal-title');
  readonly selectContentPublicLocator: Locator = this.page.locator('label.ant-radio-wrapper >> nth=0');
  readonly selectContentPrivateLocator: Locator = this.page.locator('label.ant-radio-wrapper >> nth=1');
  readonly selectContentSpecificLocator: Locator = this.page.locator('label.ant-radio-wrapper >> nth=2');
  readonly toggleSelfEnrollmentLocator: Locator = this.page.locator('#isSelfEnrollEnabled');
  readonly toggleExpiryDayLocator: Locator = this.page.locator('#isExpiryDay');
  readonly toggleReEnrollWhenPassLocator: Locator = this.page.locator('#isReEnrollEnabled');
  readonly toggleReEnrollWhenExpireLocator: Locator = this.page.locator('#isReEnrollExpireEnabled');
  readonly toggleStudyDayLocator: Locator = this.page.locator('#isExpiryDay');
  readonly reEnrollExpireDayInputLocator: Locator = this.page.locator('#reEnrollExpireDay');
  readonly studyDayInputLocator: Locator = this.page.locator('#expiryDay');
  readonly reEnrollExpireDayEmptyErrorLocator: Locator = this.page.getByText(
    'กรุณากรอกจำนวนวันที่เปิดให้ลงทะเบียนซ้ำ',
    { exact: true },
  );
  readonly reEnrollExpireDayZeroErrorLocator: Locator = this.page.getByText(
    'กรุณากรอกจำนวนวันที่เปิดให้ลงทะเบียนซ้ำ โดยค่าต้องมากกว่า 0 เสมอ',
  );
  readonly reEnrollPassDayInputLocator: Locator = this.page.locator('#reEnrollDay');
  readonly reEnrollPassDayEmptyErrorLocator: Locator = this.page.getByText('กรุณากรอกจำนวนวันที่เปิดให้ลงทะเบียนซ้ำ', {
    exact: true,
  });
  readonly reEnrollPassDayZeroErrorLocator: Locator = this.page.getByText(
    'กรุณากรอกจำนวนวันที่เปิดให้ลงทะเบียนซ้ำ โดยค่าต้องมากกว่า 0 เสมอ',
  );
  readonly studyDayEmptyErrorLocator: Locator = this.page.getByText('กรุณากรอกจำนวนวันที่เรียนได้', {
    exact: true,
  });
  readonly studyDayZeroErrorLocator: Locator = this.page.getByText(
    'กรุณากรอกจำนวนวันที่เรียนได้ โดยค่าต้องมากกว่า 0 เสมอ',
  );
  readonly selectEnrollImmediateLocator: Locator = this.page.locator('input[type="radio"][value="IMMEDIATE"]');
  readonly selectEnrollWithRoundLocator: Locator = this.page.locator('input[type="radio"][value="PRE_ENROLL"]');
  readonly selectPublicAccessLocator: Locator = this.page.locator('input[type="radio"][value="PUBLIC"]');
  readonly selectPrivateAccessLocator: Locator = this.page.locator(
    'input[type="radio"][value="PRIVATE_NO_USER_GROUP"]',
  );
  readonly selectSpecificUserGroupAccessLocator: Locator = this.page.locator(
    'input[type="radio"][value="PRIVATE_WITH_USER_GROUP"]',
  );
  readonly userGroupSearchInputLocator: Locator = this.page
    .getByRole('dialog')
    .locator('.ant-select')
    .locator('input[role="combobox"]');
  readonly userGroupOptionLocator: Locator = this.page.locator('.ant-select-item-option-content');
  readonly drawerCloseButtonLocator: Locator = this.page.getByRole('dialog').getByRole('button', { name: 'Close' });
  readonly addUserGroupButtonLocator: Locator = this.page.getByRole('button', { name: 'เพิ่มกลุ่มผู้ใช้งาน' });
  readonly userGroupTableLocator: Locator = this.page.locator('.user-group-table');
  readonly selectedUserGroupsLocator: Locator = this.page.locator('.ant-table-tbody tr:not(.ant-table-placeholder)');
  readonly userGroupTableRowLocator: Locator = this.page.locator('.ant-table-tbody tr[data-row-key]');
  readonly userGroupNameCellLocator: Locator = this.page.locator(
    '.ant-table-tbody tr[data-row-key] .ant-table-cell-ellipsis span',
  );
  readonly noUserGroupErrorLocator: Locator = this.page.getByText('กรุณาเลือกกลุ่มผู้ใช้งานอย่างน้อย 1 กลุ่ม');
  readonly removeUserGroupLocator: Locator = this.page
    .locator('.user-group-table')
    .getByRole('button', { name: 'นำออก' });
  readonly toggleFastForwardLocator: Locator = this.page.locator('#isSeekEnabled');
  readonly toggleVideoSpeedLocator: Locator = this.page.locator('#isVideoSpeedEnabled');
  readonly toggleCountdownArticleLocator: Locator = this.page.locator('#isCountdownArticle');
  readonly togglePlayVideoBackgroundLocator: Locator = this.page.locator('#isPlayVideoBackgroundDisabled');
  readonly toggleAttentionCheckLocator: Locator = this.page.locator('#isAttentionCheckEnabled');
  readonly toggleLearnableFullscreenLocator: Locator = this.page.locator('#isLearnableFullscreen');
  readonly addCertificateButtonLocator: Locator = this.page.getByRole('button', { name: 'เพิ่มประกาศนียบัตร' });
  readonly selectTypeCertificateDropdownLocator: Locator = this.page.locator('#slugName');
  readonly selectTypeInsuranceDropdownLocator: Locator = this.page.getByLabel('ประเภทการประกัน');
  readonly selectTypeOicReceiveLocator: Locator = this.page.getByText('อบรมวิชาชีพการประกันภัย - ขอรับ');
  readonly selectTypeLifeLocator: Locator = this.page.getByText('ประกันชีวิต');
  readonly toggleUseCertificateLocator: Locator = this.page.locator('#isCertificateEnabled');
  readonly toggleEnableQuizCriteriaLocator: Locator = this.page.getByRole('switch');
  readonly inputPassScoreQuizLocator: Locator = this.page.locator('input.ant-input-number-input');
  readonly collapsedCurriculumButtonLocator: Locator = this.page.locator(
    '.ant-btn-variant-outlined.ant-btn-lg.ant-btn-block',
  );
  readonly confirmCreateNewCourseVersionLocator: Locator = this.page.getByText('ยืนยันการร่างเวอร์ชันใหม่ใช่หรือไม่?');

  // toastMsg
  readonly saveSuccessToastLocator: Locator = this.page.getByText('บันทึกข้อมูลสำเร็จ');
  readonly addInstructorSuccessToastLocator: Locator = this.page.getByText('เพิ่มผู้สอนสำเร็จ');
  readonly createInstructorSuccessToastLocator: Locator = this.page.getByText('สร้างผู้สอนสำเร็จ');
  readonly toastMsgCancelSuccessLocator: Locator = this.page.getByText('ยกเลิกร่างสำเร็จ');
  readonly toastMsgPublishNewVersionLocator: Locator = this.page.getByText('เผยแพร่เวอร์ชันใหม่่สำเร็จ');
  readonly toastMsgDraftVersionLocator: Locator = this.page.getByText('ร่างเวอร์ชันใหม่สำเร็จ');
  readonly selectOrganizationCertificateIdDropdownLocator: Locator = this.page.locator('#organizationCertificateId');
  readonly courseEditCertificateButtonLocator: Locator = this.page
    .locator('div.detail-card-header')
    .filter({ hasText: 'ตั้งค่าประกาศนียบัตร' })
    .locator('button:has-text("แก้ไข")');
  readonly studyCriteriaMenuLocator: Locator = this.page.getByRole('menuitem', { name: 'เกณฑ์การเรียน' });
  readonly oicReportMenuLocator: Locator = this.page.getByRole('menuitem', { name: 'ข้อมูลออกรายงาน' });

  // OIC Report Import Elements
  readonly downloadTemplateButtonLocator: Locator = this.page.getByRole('link', {
    name: 'ดาวน์โหลดไฟล์ตัวอย่าง',
  });
  readonly importDataButtonLocator: Locator = this.page.getByRole('button', { name: 'นำเข้าข้อมูล' });
  readonly uploadFileAreaLocator: Locator = this.page.locator('div.ant-upload-drag-container');
  readonly saveImportButtonLocator: Locator = this.page.getByRole('button', { name: 'บันทึก' });
  readonly confirmImportButtonLocator: Locator = this.page.getByRole('button', { name: 'ยืนยัน' });
  readonly successImportMessageLocator: Locator = this.page.getByText('นำเข้าข้อมูลสำเร็จ');
  readonly emptyFileErrorMessageLocator: Locator = this.page.getByText('ไม่พบแถวข้อมูล กรุณาลองใหม่อีกครั้ง');
  readonly incompleteDataErrorMessageLocator: Locator = this.page.locator('text=/แถวที่ \\d+ ใส่ข้อมูลไม่ครบ/');

  readonly courseEditQuizCriteriaButtonLocator: Locator = this.page
    .locator('div.detail-card-header')
    .filter({ hasText: 'แบบทดสอบ' })
    .locator('button:has-text("แก้ไข")');

  readonly submitSaveEditCourseButtonLocator: Locator = this.page
    .locator('div.ant-modal-content')
    .filter({ hasText: 'ยืนยันการร่างเวอร์ชันใหม่ใช่หรือไม่?' })
    .locator('button:has-text("ยืนยัน")');

  // Instructor creation workflow locators
  readonly searchInstructorInputLocator: Locator = this.page.locator('#instructorId');
  readonly createNewInstructorLocator: Locator = this.page.getByRole('button', { name: 'สร้างผู้สอนใหม่...' });
  readonly instructorNameInputLocator: Locator = this.page.locator('#firstname');
  readonly instructorLastNameInputLocator: Locator = this.page.locator('#lastname');
  readonly instructorEmailInputLocator: Locator = this.page.locator('#email');
  readonly instructorPhoneInputLocator: Locator = this.page.locator('#phone');
  readonly instructorHighlightDescriptionInputLocator: Locator = this.page.locator('#highlightDescription');
  readonly instructorBiologyEditorLocator: Locator = this.page.locator('.tiptap.ProseMirror[contenteditable="true"]');
  readonly createInstructorSaveButtonLocator: Locator = this.page
    .locator('.ant-drawer-content')
    .filter({ has: this.page.getByText('สร้างผู้สอน') })
    .getByRole('button', { name: 'สร้าง' });
  readonly instructorDrawerCloseButtonLocator: Locator = this.page.getByRole('button', { name: 'Close' });
  readonly instructorDrawerDiscardButtonLocator: Locator = this.page.getByRole('button', { name: 'ละทิ้ง' });
  readonly instructorAvatarUploadLocator: Locator = this.page.locator('input#avatar[type="file"]');
  readonly selectNewInstructorLocator: Locator = this.page.locator('.ant-select-item-option-content');
  readonly addInstructorLocator: Locator = this.page.getByRole('button', { name: 'เพิ่มผู้สอน' });
  readonly instructorListLocator: Locator = this.page.locator('.ant-table-tbody');

  // Instructor reordering locators
  readonly instructorTableLocator: Locator = this.page.locator('.ant-table-tbody[data-rbd-droppable-id="instructors"]');
  readonly instructorDndTableLocator: Locator = this.page.locator('.ant-table-tbody tr[data-rbd-draggable-id]');
  readonly instructorDragHandleLocator: Locator = this.page.locator('span[data-rbd-drag-handle-draggable-id]');
  readonly firstInstructorDragHandleLocator: Locator = this.instructorDndTableLocator
    .first()
    .locator('span[data-rbd-drag-handle-draggable-id]');
  readonly secondInstructorDragHandleLocator: Locator = this.instructorDndTableLocator
    .nth(1)
    .locator('span[data-rbd-drag-handle-draggable-id]');
  readonly instructorListInDndTableLocator: Locator = this.page
    .locator('.ant-table-tbody tr')
    .locator('.ant-table-cell.ellipsis .ant-typography')
    .first();
  readonly instructorNameListLocator: Locator = this.page.locator('.ant-list-item-meta-title');
  readonly deleteInstructorButtonLocator: Locator = this.page.locator('.ant-table-cell button .fa-circle-minus');

  // Delete instructor modal locators
  readonly deleteInstructorModalLocator: Locator = this.page
    .locator('.ant-modal-content')
    .filter({ hasText: 'ต้องการนำผู้สอนออกหรือไม่' });
  readonly confirmDeleteInstructorButtonLocator: Locator = this.deleteInstructorModalLocator.getByRole('button', {
    name: 'นำออก',
  });
  readonly cancelDeleteInstructorButtonLocator: Locator = this.deleteInstructorModalLocator.getByRole('button', {
    name: 'ยกเลิก',
  });

  generalCourseDetailElementsPage: GeneralCourseDetailElmentsPage;

  certificateCourseDetailElementsPage: CertificateCourseDetailElementsPage;
  constructor(page: Page) {
    super(page, '/course');
    this.generalCourseDetailElementsPage = new GeneralCourseDetailElmentsPage(
      page,
      page.locator('.ant-drawer-content'),
    );
    this.certificateCourseDetailElementsPage = new CertificateCourseDetailElementsPage(
      page,
      page.locator('.ant-drawer-content'),
    );
  }
  async verifyCourseDetailURL(): Promise<this> {
    await this.page.waitForURL(new RegExp('.*/?tab=general&version='));
    await this.page.waitForTimeout(1000);
    return this;
  }

  async accessMenuInstructor(): Promise<this> {
    await this.courseDetailInstructorsMenu.click();
    await this.page.waitForLoadState();

    return this;
  }

  async accessTabGeneralInfo(): Promise<this> {
    await this.tabGeneralInfoButtonLocator.click();
    await this.page.waitForLoadState();

    return this;
  }

  async accessMenuAccessing(): Promise<this> {
    await this.accessingMenuLocator.click();
    await this.page.waitForLoadState();

    return this;
  }

  async addInstructor(instructorName: string): Promise<this> {
    await this.inputInstructorsLocator.fill(instructorName);
    await this.selectItemInstructorsLocator.click();
    await this.addInstructorButtonLocator.click();

    await this.page.waitForLoadState();

    return this;
  }

  async settingCourseAccessTypePrivate(): Promise<this> {
    await expect(this.headingSettingAccessingLocator).toBeVisible();
    await this.editButtonLocator.nth(0).click();

    await expect(this.selectContentPrivateLocator).toContainText('เนื้อหาการเรียนแบบส่วนบุคคล');
    await this.selectContentPrivateLocator.click();

    await this.page.waitForLoadState();

    return this;
  }

  async settingCourseAccessTypePublic(): Promise<this> {
    await expect(this.headingSettingAccessingLocator).toBeVisible();
    await this.editButtonLocator.nth(0).click();

    await expect(this.selectContentPublicLocator).toContainText('เนื้อหาการเรียนแบบสาธารณะ');
    await this.selectContentPublicLocator.click();

    await this.page.waitForLoadState();

    return this;
  }

  async enabledSelfEnroll(): Promise<this> {
    await expect(this.headingSettingEnrollmentLocator).toBeVisible();
    await this.editButtonLocator.nth(0).click();
    await this.toggleSelfEnrollmentLocator.click();

    return this;
  }

  async enabledExpiryDay(): Promise<this> {
    await expect(this.headingSettingTotalDateTrainingLocator).toBeVisible();
    await this.editButtonLocator.nth(1).click();
    await this.toggleExpiryDayLocator.click();

    return this;
  }
  async enabledReEnrollDayPassed(): Promise<this> {
    await expect(this.headingSettingReEnrollmentPassLocator).toBeVisible();
    await this.editButtonLocator.nth(2).click();
    await this.toggleReEnrollWhenPassLocator.click();

    return this;
  }

  async enabledReEnrollDayExpired(): Promise<this> {
    await expect(this.headingSettingReEnrollmentExpireLocator).toBeVisible();
    await this.editButtonLocator.nth(3).click();
    await this.toggleReEnrollWhenExpireLocator.click();

    return this;
  }

  async startTrainingNow(): Promise<this> {
    await expect(this.headingSettingReEnrollmentExpireLocator).toBeVisible();
    await this.editButtonLocator.nth(4).click();
    await this.selectEnrollImmediateLocator.click();

    return this;
  }

  async enabledFastForwardVideo(): Promise<this> {
    await expect(this.headingSettingGeneralLocator).toBeVisible();
    await this.editButtonLocator.nth(0).click();
    await this.toggleFastForwardLocator.click();

    return this;
  }

  async enabledVideoSpeed(): Promise<this> {
    await expect(this.headingSettingGeneralLocator).toBeVisible();
    await this.editButtonLocator.nth(0).click();
    await this.toggleVideoSpeedLocator.click();

    return this;
  }

  async enabledCountdownArticle(): Promise<this> {
    await expect(this.headingSettingGeneralLocator).toBeVisible();
    await this.editButtonLocator.nth(0).click();
    await this.toggleCountdownArticleLocator.click();

    return this;
  }

  async enabledPlayVideoBackground(): Promise<this> {
    await expect(this.headingSettingGeneralLocator).toBeVisible();
    await this.editButtonLocator.nth(0).click();
    await this.togglePlayVideoBackgroundLocator.click();

    return this;
  }

  async accessTabCurriculum(): Promise<this> {
    await this.tabCurriculumButtonLocator.click();
    await this.loadingAnimation.waitFor({ state: 'hidden' });
    await this.page.waitForLoadState();

    return this;
  }

  async accessMenuCertificate(): Promise<this> {
    await this.certificateMenuLocator.click();

    return this;
  }

  async accessMenuStudyCriteriaMenuLocator(): Promise<this> {
    await this.studyCriteriaMenuLocator.click();

    return this;
  }

  async accessMenuOicReport(): Promise<this> {
    await this.oicReportMenuLocator.click();

    return this;
  }

  async downloadOicReportTemplate(): Promise<string> {
    const [download] = await Promise.all([
      this.page.waitForEvent('download'),
      this.downloadTemplateButtonLocator.click(),
    ]);
    const path = await download.path();

    return path;
  }

  async clickImportDataButton(): Promise<this> {
    await this.importDataButtonLocator.click();

    return this;
  }

  async uploadFile(filePath: string): Promise<this> {
    const [fileChooser] = await Promise.all([
      this.page.waitForEvent('filechooser'),
      this.uploadFileAreaLocator.click(),
    ]);
    await fileChooser.setFiles(filePath);

    return this;
  }

  async confirmImport(): Promise<this> {
    // First click บันทึก button in the drawer
    await this.saveImportButtonLocator.click();

    // Then click ยืนยัน button in the confirmation modal
    await this.confirmImportButtonLocator.click();

    return this;
  }

  async accessMenuLearning(): Promise<this> {
    await this.learningMenuLocator.click();

    return this;
  }

  async accessMenuEnroll(): Promise<this> {
    await this.enrollMenuLocator.click();

    return this;
  }

  async accessEditEnroll(): Promise<this> {
    await this.editEnrollButtonLocator.click();

    return this;
  }

  async accessEnableReEnroll(): Promise<this> {
    await this.enableReEnrollButtonLocator.click();
    await expect(this.verifyTextReEnroll).toBeVisible();

    return this;
  }

  async fillReEnrollmentExpireDayInput(days: string): Promise<this> {
    await this.reEnrollExpireDayInputLocator.fill(days);

    return this;
  }

  async fillReEnrollmentPassDayInput(days: string): Promise<this> {
    await this.reEnrollPassDayInputLocator.fill(days);

    return this;
  }

  async fillStudyDayInput(days: string): Promise<this> {
    await this.studyDayInputLocator.fill(days);

    return this;
  }

  async accessEditCertificate(): Promise<this> {
    await this.courseEditCertificateButtonLocator.click();

    return this;
  }

  async accessEditCourseFeatures(): Promise<this> {
    await this.editCourseFeaturesButtonLocator.click();

    return this;
  }

  async accessEditCriteria(): Promise<this> {
    await this.editCriteriaButtonLocator.click();

    return this;
  }

  async accessEditQuizCriteria(): Promise<this> {
    await this.courseEditQuizCriteriaButtonLocator.click();

    return this;
  }

  async submitSaveButton(): Promise<this> {
    await this.saveButtonLocator.click();
    if (await this.submitSaveEditCourseButtonLocator.isVisible({ timeout: 3000 })) {
      await this.submitSaveEditCourseButtonLocator.click();
      await this.page.waitForLoadState();
    }

    return this;
  }

  async clickCompleteButton(): Promise<this> {
    await this.completeButtonLocator.click();

    return this;
  }

  async clickPublishContentButton(): Promise<this> {
    await this.publishContentButtonLocator.click();

    return this;
  }

  async accessInstructorCourses(courseName: string, addInstructors: string): Promise<this> {
    await this.courseNameInputLocator.fill(courseName);
    await this.loadingSkeleton.waitFor({ state: 'hidden' });
    await this.searchButtonLocator.click();
    await this.loadingAnimation.waitFor({ state: 'hidden' });
    await expect(this.editInstructorButtonLocator).toBeVisible();
    await this.editInstructorButtonLocator.click();
    await this.courseDetailInstructorsMenu.click();
    await this.editButtonLocator.click();
    await this.inputInstructorsLocator.fill(addInstructors);
    await this.selectInstructorsLocator.click();
    await this.addInstructorButtonLocator.click();
    await this.saveButtonLocator.click();
    await expect(this.toastMsgDraftVersionLocator).toBeVisible();
    await this.toastMsgDraftVersionLocator.waitFor({ state: 'hidden' });
    await this.publishedNewVersionButtonLocator.click();
    await expect(this.toastMsgPublishNewVersionLocator).toBeVisible();
    await this.loadingSkeleton.waitFor({ state: 'hidden' });
    await expect(this.backButtonLocator).toBeVisible();
    await this.backButtonLocator.click();
    await this.loadingSkeleton.waitFor({ state: 'hidden' });

    return this;
  }

  async verifyCourseSyncFromB2c(): Promise<this> {
    await this.tabCurriculumButtonLocator.click();
    return this;
  }

  async enabledAttentionCheck(): Promise<this> {
    await expect(this.headingSettingInterestLocator).toBeVisible();
    await this.editButtonLocator.nth(1).click();
    await this.toggleAttentionCheckLocator.click();

    return this;
  }

  async enabledLearningForceFullscreen(): Promise<this> {
    await expect(this.headingSettingGeneralLocator).toBeVisible();
    await this.editButtonLocator.nth(0).click();
    await this.toggleLearnableFullscreenLocator.click();

    return this;
  }

  async addCertificate(): Promise<this> {
    await expect(this.headingSettingCertificateLocator).toBeVisible();
    await this.editButtonLocator.nth(0).click();
    await this.addCertificateButtonLocator.click();
    await this.selectOrganizationCertificateIdDropdownLocator.click();
    await this.selectTypeOicReceiveLocator.click();
    await this.saveButtonLocator.click();

    return this;
  }

  async settingPassQuizCriteria(scoreQuiz: string): Promise<this> {
    // await expect(this.headingConditionCertificateLocator).toBeVisible();
    await this.editButtonLocator.nth(1).click();
    await this.toggleEnableQuizCriteriaLocator.nth(0).click();
    await this.page.waitForLoadState();
    await this.inputPassScoreQuizLocator.fill(scoreQuiz);

    return this;
  }

  async cancelDraftVersion(): Promise<this> {
    await this.deleteDraftVersionButtonLocator.click();
    await this.page.waitForLoadState();

    return this;
  }

  async confirmCancelDraftVersion(): Promise<this> {
    await expect(this.headingConfirmCancelLocator).toContainText('ยืนยันการยกเลิกร่างใช่หรือไม่');
    await this.confirmButtonLocator.click();
    await this.page.waitForLoadState();
    await expect(this.toastMsgCancelSuccessLocator).toBeVisible();
    await this.toastMsgCancelSuccessLocator.waitFor({ state: 'hidden' });

    return this;
  }

  async toastMessageSaveSuccess(): Promise<this> {
    await expect(this.saveSuccessToastLocator).toBeVisible();
    await this.saveSuccessToastLocator.waitFor({ state: 'hidden' });

    return this;
  }

  async verifyCreateDraftSuccess(): Promise<this> {
    await expect(this.toastMsgDraftVersionLocator).toBeVisible();
    await this.toastMsgDraftVersionLocator.waitFor({ state: 'hidden' });

    return this;
  }

  async confirmCreateNewCourseVersion(): Promise<this> {
    await expect(this.confirmCreateNewCourseVersionLocator).toBeVisible();
    await this.confirmButtonLocator.click();

    return this;
  }

  async editCourseName(courseName: string): Promise<this> {
    await this.inputCourseNameLocator.fill(courseName);

    return this;
  }

  async fillCourseDescription(description: string): Promise<this> {
    await this.courseDescriptionEditorLocator.fill(description);

    return this;
  }

  async validateSelectedUserGroup(userGroupName: string): Promise<this> {
    await expect(this.userGroupNameCellLocator).toContainText(userGroupName);
    return this;
  }

  async editCurriculum(): Promise<this> {
    await this.editButtonLocator.click();
    await this.loadingSpinLocator.waitFor({ state: 'hidden' });

    const buttonNameText = await this.collapsedCurriculumButtonLocator.innerText();

    if (buttonNameText.includes('แสดงทั้งหมด')) {
      await this.collapsedCurriculumButtonLocator.click();
    } else if (buttonNameText.includes('ซ่อนทั้งหมด')) {
      await this.loadingSkeleton.waitFor({ state: 'hidden' });
    }

    return this;
  }

  async deleteCourseContent(contentItem: number): Promise<this> {
    await expect(this.selectContentInCurriculumLocator.nth(contentItem)).toBeVisible();
    await expect(this.toggleInnerContentButtonLocator.nth(contentItem)).toBeVisible();
    await this.toggleInnerContentButtonLocator.nth(contentItem - 1).click();
    expect(this.toggleInnerContentButtonLocator.nth(contentItem - 1).locator('[aria-checked="false"]')).toBeTruthy();
    await this.deleteContentButtonLocator.nth(contentItem).click();

    return this;
  }

  async collapsedContentInCurriculum(contentItem: number): Promise<this> {
    await expect(this.selectContentInCurriculumLocator.nth(contentItem)).toBeVisible();
    await this.loadingAnimation.waitFor({ state: 'hidden' });
    await this.toggleInnerContentButtonLocator.nth(contentItem - 1).click();
    expect(this.toggleInnerContentButtonLocator.nth(contentItem - 1).locator('[aria-checked="false"]')).toBeTruthy();

    return this;
  }

  async confirmDeleteContent(): Promise<this> {
    await expect(this.confirmDeleteButtonLocator).toBeVisible();
    await this.confirmDeleteButtonLocator.click();

    return this;
  }

  async confirmDeleteCourse(): Promise<this> {
    await expect(this.confirmDeleteButtonLocator).toBeVisible();
    await this.confirmDeleteButtonLocator.click();

    return this;
  }

  async confirmDiscard(): Promise<this> {
    await expect(this.confirmDiscardButtonLocator).toBeVisible();
    await this.confirmDiscardButtonLocator.click();

    return this;
  }

  async saveCourseContent(): Promise<this> {
    await this.completeButtonLocator.click();
    await this.loadingSpinLocator.waitFor({ state: 'hidden' });

    return this;
  }

  async publishNewVersioning(): Promise<this> {
    await this.publishedNewVersionButtonLocator.click();
    await expect(this.toastMsgPublishNewVersionLocator).toBeVisible();
    await this.toastMsgPublishNewVersionLocator.waitFor({ state: 'hidden' });
    await this.loadingSpinLocator.waitFor({ state: 'hidden' });

    return this;
  }

  async accessCourseGeneralDetail(courseName: string): Promise<this> {
    await expect(this.detailCourseNameInfoLocator.nth(0)).toHaveText(courseName);
    await this.editButtonLocator.click();
    await this.loadingSpinLocator.waitFor({ state: 'hidden' });

    return this;
  }

  async createNewInstructor(instructorData: {
    firstName: string;
    lastName?: string;
    email?: string;
    phone?: string;
    highlightDescription?: string;
    biology?: string;
  }): Promise<this> {
    // Click search instructor input to open dropdown
    await this.searchInstructorInputLocator.click();

    // Click create new instructor button
    await this.createNewInstructorLocator.click();

    // Fill required first name
    await this.instructorNameInputLocator.fill(instructorData.firstName);

    // Fill optional fields if provided
    if (instructorData.lastName) {
      await this.instructorLastNameInputLocator.fill(instructorData.lastName);
    }

    if (instructorData.email) {
      await this.instructorEmailInputLocator.fill(instructorData.email);
    }

    if (instructorData.phone) {
      await this.instructorPhoneInputLocator.fill(instructorData.phone);
    }

    if (instructorData.highlightDescription) {
      await this.instructorHighlightDescriptionInputLocator.fill(instructorData.highlightDescription);
    }

    if (instructorData.biology) {
      await this.instructorBiologyEditorLocator.fill(instructorData.biology);
    }

    // Save the new instructor
    await this.createInstructorSaveButtonLocator.click();

    // Wait for success message and then wait for it to disappear
    await expect(this.createInstructorSuccessToastLocator).toBeVisible();

    return this;
  }

  async validateInstructorInList(instructorName: string): Promise<this> {
    await expect(this.instructorListLocator).toContainText(instructorName);
    return this;
  }

  async reorderInstructors(): Promise<this> {
    // Wait for the instructor table to be visible
    await expect(this.instructorTableLocator).toBeVisible();

    // Wait for instructor rows to be loaded
    await expect(this.instructorDndTableLocator.first()).toBeVisible();

    // Wait a moment for the table to fully render
    await this.page.waitForTimeout(1000);

    // Get the initial order of instructors for validation
    const instructorNames = await this.instructorListInDndTableLocator.allTextContents();
    console.log('Initial instructor order:', instructorNames);

    // Perform drag and drop to reorder - move first instructor to second position
    await this.dragAndDropInstructor(0, 1);

    // Wait for the reordering animation/update to complete
    await this.page.waitForTimeout(1000);

    return this;
  }

  async dragAndDropInstructor(fromIndex: number, toIndex: number): Promise<this> {
    // Get source and target elements
    const sourceHandle = this.instructorDndTableLocator
      .nth(fromIndex)
      .locator('span[data-rbd-drag-handle-draggable-id]');
    const targetRow = this.instructorDndTableLocator.nth(toIndex);

    // Wait for elements to be visible
    await expect(sourceHandle).toBeVisible();
    await expect(targetRow).toBeVisible();

    // Get bounding boxes for drag and drop
    const sourceBounds = await sourceHandle.boundingBox();
    const targetBounds = await targetRow.boundingBox();

    if (!sourceBounds || !targetBounds) {
      throw new Error('Could not get bounding boxes for drag and drop');
    }

    // Perform drag and drop using mouse actions
    await this.page.mouse.move(sourceBounds.x + sourceBounds.width / 2, sourceBounds.y + sourceBounds.height / 2);
    await this.page.mouse.down();

    // Move to target position
    await this.page.mouse.move(targetBounds.x + targetBounds.width / 2, targetBounds.y + targetBounds.height / 2, {
      steps: 10,
    });
    await this.page.waitForTimeout(500); // Brief pause for visual feedback

    await this.page.mouse.up();

    // Wait for reorder to complete
    await this.page.waitForTimeout(1000);

    return this;
  }

  async validateInstructorOrder(expectedOrder: string[]): Promise<this> {
    // Wait for instructor list to be visible and stable
    await expect(this.instructorNameListLocator.first()).toBeVisible();

    // Wait a moment for the order to settle after reordering
    await this.page.waitForTimeout(1000);

    const currentOrder = await this.instructorNameListLocator.allTextContents();
    const trimmedCurrentOrder = currentOrder.map((name) => name.trim());

    console.log('Expected order:', expectedOrder);
    console.log('Current order:', trimmedCurrentOrder);

    // Validate that the order has changed as expected
    for (let i = 0; i < expectedOrder.length; i++) {
      expect(trimmedCurrentOrder[i]).toContain(expectedOrder[i]);
    }

    return this;
  }

  async deleteInstructor(instructorIndex: number): Promise<this> {
    const deleteButton = this.instructorDndTableLocator.nth(instructorIndex).locator('button:has(.fa-circle-minus)');
    await expect(deleteButton).toBeVisible();
    await deleteButton.click();

    // Wait for confirmation modal to appear and confirm deletion
    await expect(this.deleteInstructorModalLocator).toBeVisible();
    await this.confirmDeleteInstructorButtonLocator.click();

    // Wait for modal to disappear
    await expect(this.deleteInstructorModalLocator).not.toBeVisible();

    return this;
  }
}
